!function(a,b,c,d){"use strict";var e,f=function(b,d){function e(){a.each(i,function(a,c){c=c.split(":");var d=c[0],e=c[1],g=e.indexOf("px")>=0?"px":e.indexOf("%")>=0?"%":"px";e=isNaN(parseFloat(e))?e:parseFloat(e),f.parallax?("direction"===d&&b.data("pd",e),"speed"===d&&b.data("ps",e),"rotate"===d&&b.data("pr",e),"opacity"===d&&b.data("po",e)):("opacity"===d&&b.data("o",e),"scale"===d&&b.data("s",e),"easing"===d&&b.data("e",e),"transX"===d&&b.data("tx",e+g),"transY"===d&&b.data("ty",e+g),"transZ"===d&&b.data("tz",e+g),"rotateX"===d&&b.data("rx",e+"deg"),"rotateY"===d&&b.data("ry",e+"deg"),"rotateZ"===d&&b.data("rz",e+"deg"),"transOrigX"===d&&b.data("ox",e+"%"),"transOrigY"===d&&b.data("oy",e+"%"),"duration"===d&&b.data("du",e+"ms"),"delay"===d&&b.data("de",e+"ms"))})}var f=a.extend({},a.fn.horizon.tmhOpts,d);b=a(b),u&&b.css({opacity:1,visibility:"visible"}),b.data("scrolling",!1).css("-webkit-backface-visibility","hidden");var i;b.is("[data-parallax]")||f.parallax?(f.parallax=!0,f.threshold=0,b.data("threshold",0),b.parent().addClass("tmh-perspective-parallax"),b.addClass("tmh-parallax-item"),i=String(b.data("parallax")).split(";"),h(b,f,!0)):(b.parent().addClass("tmh-perspective"),i=String(b.data("animate-in")).split(";")),a.inArray("preset",String(i).split(":"))!=-1?i.filter(function(b){if("preset"===b.split(":")[0]){i.splice(a.inArray(b,i),1);var c=String(t[b.split(":")[1]]).split(";");i=a.merge(c,i).filter(Boolean),"undefined"!=typeof i[0]&&e()}}):(i=i.filter(Boolean),"undefined"!==i[0]&&e()),a(c).on("scroll",function(){g(b,f,!1)}),a(c).on("resize",function(){g(b,f,!1)}),f.parallax||j(b,f),g(b,f)},g=function(a,b,c){a.data("scrolling")||(requestAnimationFrame(function(){h(a,b,c)}),a.data("scrolling",!0))},h=function(b,d,e){if(i(b,d)||e)if(d.parallax){if(u)return!1;var f=b.data("pd")?b.data("pd"):"vertical",g=b.data("ps")?b.data("ps"):d.parallaxSpeed,h=b.data("pr")?b.data("pr"):"none",j=b.data("po")?b.data("po"):"none",l=a(c),m=l.scrollTop(),o=b.offset().top,p=l.height()*g,q=b.parent().height()+b.parent().offset().top-m,r=-((o-m-l.height())*g)-p,t="horizontal"===f?r+"px":0,v="vertical"===f?r+"px":0;-(.1*(o-m-l.height()))-p;h="clockwise"===h?.02*-r+"deg":"anticlockwise"===h?.02*r+"deg":0,j="fade"===j?q/b.parent().height():1,n&&(l.width()>=768?k(b,j.toFixed(2),t,v,0,h,h,h,1,"50%","50%",0,0,"ease-out",d):k(b,1,0,0,0,0,0,0,1,0,0,"100ms",0,"swing",d))}else{var w=b.data("ox")?b.data("ox"):"50%",x=b.data("oy")?b.data("oy"):"50%",y=b.data("du")?b.data("du"):d.speed,z=b.data("de")?b.data("de"):0,A=b.data("e")?s[b.data("e")]:s[d.easing];n?k(b,1,0,0,0,0,0,0,1,w,x,y,z,A,d):b.css({visibility:"visible"}).stop().animate({opacity:1},d.speed,d.easingFallback,function(){d.inView&&d.inView()})}b.data("scrolling",!1)},i=function(b,d){var e=a(c).scrollTop(),f=e+a(c).height(),g=b.data("threshold")?parseFloat(b.data("threshold")):d.threshold,h=b.data("ty")?parseFloat(b.data("ty")):0,i=b.offset().top,k=b.offset().top-h,l=k+b.outerHeight()-b.outerHeight()*g,m=k+b.outerHeight()*g;return(i-e>a(c).height()||i-e<-b.outerHeight())&&(d.recurring&&j(b,d),d.outOfView&&d.outOfView()),f>=m&&e<=l},j=function(a,b){if(u)return!1;var c=a.data("o")?a.data("o"):0,d=a.data("tx")?a.data("tx"):0,e=a.data("ty")?a.data("ty"):0,f=a.data("tz")?a.data("tz"):0,g=a.data("rx")?a.data("rx"):0,h=a.data("ry")?a.data("ry"):0,i=a.data("rz")?a.data("rz"):0,j=a.data("s")?a.data("s"):1;n?a.css({transition:"none",transform:"translate3d("+d+", "+e+", "+f+" )rotateX("+g+") rotateY("+h+") rotateZ("+i+") scale3d("+j+", "+j+", "+j+")",opacity:c,visibility:"hidden"}):a.css({opacity:0})},k=function(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){var s={};s.transform="translate3d("+d+", "+e+", "+f+") rotateX("+g+") rotateY("+h+") rotateZ("+i+") scale3d("+j+", "+j+", "+j+")",s.transitionProperty=q+", opacity",s.transformOrigin=k+" "+l+" 0",s.transitionDuration=m,s.transitionDelay=n,s.transitionTimingFunction=o,s.visibility="visible",s.opacity=c,b.css(s).on(r,function(b){b.stopPropagation(),a(this).off(r),p.inView&&p.inView()})},l=b.body||b.documentElement,m=l.style,n=m.transition!==d||m.WebkitTransition!==d||m.MozTransition!==d||m.MsTransition!==d||m.OTransition!==d,o=["WebkitTransform","MozTransform","OTransform","msTransform"];for(var p in o)m[o[p]]!==d&&(e="-"+o[p].replace("Transform","").toLowerCase());var q=e+"-transform",r="webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",s={linear:"cubic-bezier(0, 0, 1, 1)",swing:"cubic-bezier(0.42, 0, 0.58, 1)",easeOutCubic:"cubic-bezier(.215,.61,.355,1)",easeInOutCubic:"cubic-bezier(.645,.045,.355,1)",easeInCirc:"cubic-bezier(.6,.04,.98,.335)",easeOutCirc:"cubic-bezier(.075,.82,.165,1)",easeInOutCirc:"cubic-bezier(.785,.135,.15,.86)",easeInExpo:"cubic-bezier(.95,.05,.795,.035)",easeOutExpo:"cubic-bezier(.19,1,.22,1)",easeInOutExpo:"cubic-bezier(1,0,0,1)",easeInQuad:"cubic-bezier(.55,.085,.68,.53)",easeOutQuad:"cubic-bezier(.25,.46,.45,.94)",easeInOutQuad:"cubic-bezier(.455,.03,.515,.955)",easeInQuart:"cubic-bezier(.895,.03,.685,.22)",easeOutQuart:"cubic-bezier(.165,.84,.44,1)",easeInOutQuart:"cubic-bezier(.77,0,.175,1)",easeInQuint:"cubic-bezier(.755,.05,.855,.06)",easeOutQuint:"cubic-bezier(.23,1,.32,1)",easeInOutQuint:"cubic-bezier(.86,0,.07,1)",easeInSine:"cubic-bezier(.47,0,.745,.715)",easeOutSine:"cubic-bezier(.39,.575,.565,1)",easeInOutSine:"cubic-bezier(.445,.05,.55,.95)",easeInBack:"cubic-bezier(.6,-.28,.735,.045)",easeOutBack:"cubic-bezier(.175, .885,.32,1.275)",easeInOutBack:"cubic-bezier(.68,-.55,.265,1.55)",easeFastSlow:"cubic-bezier(.11,.69,.66,1.01)",easeBounceBack:"cubic-bezier(.16,1.36,.57,.96)",easeBounceBackHard:"cubic-bezier(.8,1.91,0,.94)",easeBounceIn:"cubic-bezier(.15,2.6,0,-0.2)",easeSwingInOut:"cubic-bezier(.35,3.8,0.3,-0.6)"},t={fadeIn:"opacity: 0;easing: swing;",slideInUpShort:"opacity:0;transY: 50px;easing:easeFastSlow;",slideInRightShort:"opacity:0;transX: 50px;easing:easeFastSlow;",slideInDownShort:"opacity:0;transY: -50px;easing:easeFastSlow;",slideInLeftShort:"opacity:0;transX: -50px;easing:easeFastSlow;",slideInUpLong:"opacity:0;transY: 250px;easing:easeFastSlow;",slideInRightLong:"opacity:0;transX: 250px;easing:easeFastSlow;",slideInDownLong:"opacity:0;transY: -250px;easing:easeFastSlow;",slideInLeftLong:"opacity:0;transX: -250px;easing:easeFastSlow;",bounceIn:"opacity:0;scale:0.7;easing:easeBounceIn;",bounceOut:"opacity:0;scale:1.4;easing:easeBounceIn;",bounceInUp:"opacity:0;transY: 250px;easing:easeBounceIn;",bounceInRight:"opacity:0;transX: 250px;easing:easeBounceIn;",bounceInDown:"opacity:0;transY: -250px;easing:easeBounceIn;",bounceInLeft:"opacity:0;transX: -250px;easing:easeBounceIn;",scaleIn:"opacity:0;scale: 0.6;easing:easeFastSlow;",scaleOut:"opacity:0;scale: 1.4;easing:easeFastSlow",flipInX:"opacity:0;rotateX: -180deg;easing:easeFastSlow;",flipInY:"opacity:0;rotateY: -180deg;easing:easeFastSlow;",spinInX:"opacity:0;rotateX: -540deg;easing:easeFastSlow;",spinInY:"opacity:0;rotateY: -540deg;easing:easeFastSlow;",helicopterIn:"opacity:0;scale: 0.6;rotateZ: -360deg;easing:easeFastSlow;",helicopterOut:"opacity:0;scale: 1.4;rotateZ: -360deg;easing:easeFastSlow;",signSwingTop:"opacity:0;rotateX:-60deg;transOrigX:50%;transOrigY:0%;easing:easeSwingInOut;",signSwingRight:"opacity:0;rotateY:-60deg;transOrigX:100%;transOrigY:50%;easing:easeSwingInOut;",signSwingBottom:"opacity:0;rotateX:-60deg;transOrigX:50%;transOrigY:100%;easing:easeSwingInOut;",signSwingLeft:"opacity:0;rotateY:-60deg;transOrigX:0%;transOrigY:50%;easing:easeSwingInOut;",wiggleX:"opacity:0;rotateX:-90deg;transOrigX:50%;transOrigY:50%;easing:easeSwingInOut;",wiggleY:"opacity:0;rotateY:-90deg;transOrigX:50%;transOrigY:50%;easing:easeSwingInOut;",dropUp:"opacity:0;transY: 250px;rotateZ:20deg;transOrigX:50%;transOrigY:50%;easing:easeBounceBackHard;",dropDown:"opacity:0;transY: -250px;rotateZ:-20deg;transOrigX:0%;transOrigY:0%;easing:easeBounceBackHard;",rollInLeft:"opacity:0;transX: -250px;transY: 200px;rotateZ: -120px;transOrigX:0%;transOrigY:0%;easing:easeFastSlow;",rollInRight:"opacity:0;transX: 250px;transY: 200px;rotateZ: 120px;transOrigX:100%;transOrigY:0%;easing:easeFastSlow;",turnInRight:"opacity:0;transX: 250px;rotateX:20deg;rotateY:75deg;transOrigX:0%;transOrigY:0%;easing:easeBounceBack;",turnInLeft:"opacity:0;transX: -250px;rotateX:20deg;rotateY:-75deg;transOrigX:100%;transOrigY:0%;easing:easeBounceBack;"},u=!1;(navigator.userAgent.match(/Android/i)||navigator.userAgent.match(/webOS/i)||navigator.userAgent.match(/iPhone/i)||navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/iPod/i)||navigator.userAgent.match(/BlackBerry/i)||navigator.userAgent.match(/Windows Phone/i))&&(u=!0),function(){for(var a=0,b=["ms","moz","webkit","o"],d=0;d<b.length&&!c.requestAnimationFrame;++d)c.requestAnimationFrame=c[b[d]+"RequestAnimationFrame"],c.cancelAnimationFrame=c[b[d]+"CancelAnimationFrame"]||c[b[d]+"CancelRequestAnimationFrame"];c.requestAnimationFrame||(c.requestAnimationFrame=function(b,d){var e=(new Date).getTime(),f=Math.max(0,16-(e-a)),g=c.setTimeout(function(){b(e+f)},f);return a=e+f,g}),c.cancelAnimationFrame||(c.cancelAnimationFrame=function(a){clearTimeout(a)})}(),a.fn.horizon=function(b){return this.each(function(){var c=a(this);if(!c.data("horizon")){var d=new f(this,b);c.data("horizon",d)}})},a.fn.horizon.tmhOpts={easing:"swing",easingFallback:"swing",speed:"1000ms",threshold:1,recurring:!0,parallax:!1,parallaxSpeed:.2,inView:null,outOfView:null}}(jQuery,document,window);