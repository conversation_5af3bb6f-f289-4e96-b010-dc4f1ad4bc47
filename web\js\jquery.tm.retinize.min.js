!function(a){"use strict";a.fn.retinizeImages=function(b){var c=a.extend({retinaSupportMobile:!1,retinaSuffix:"@2x"},b),d=function(){var b,d=a(this);if(d.is("img"))b=d.attr("src");else if("none"!==d.css("background-image"))b=d.css("background-image").replace(/^url\(["']?/,"").replace(/["']?\)$/,"");else{if(!d.is("[data-2x]"))return!1;b=d.data("2x")}if(e&&!c.retinaSupportMobile&&!d.is("[data-retina-mobile]")||d.is("[data-no-retina]")||b.match(/\.(svg)/i)||b.indexOf(c.retinaSuffix)>=0)return!1;if(window.isRetinaDevice()){var f=b.substr(b.lastIndexOf("."));b=b.replace(f,c.retinaSuffix+f),a.ajax({type:"GET",url:b,success:function(){a("<img/>").attr("src",b).one("load",function(){d.is("img")?d.attr("src",b):d.css("background-image","url("+b+")")})}})}};window.isRetinaDevice=function(){var a="(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-resolution: 1.5dppx)";return!!(this.devicePixelRatio>1||this.matchMedia&&this.matchMedia(a).matches)};var e=!1;return(navigator.userAgent.match(/Android/i)||navigator.userAgent.match(/webOS/i)||navigator.userAgent.match(/iPhone/i)||navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/iPod/i)||navigator.userAgent.match(/BlackBerry/i)||navigator.userAgent.match(/Windows Phone/i))&&(e=!0),this.each(d)}}(jQuery);