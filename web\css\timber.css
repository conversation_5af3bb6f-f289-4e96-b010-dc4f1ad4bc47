/*------------------------------------------------------------------
Timber Framework
Version: 1.9.7;
Author: ThemeMountain
Copyright: ThemeMountain
License: MIT

[Table of contents]

1. Common
2. Timber Grid
3. Helper Classes
4. Prototype Classes
5. Main Navigation
6. Side Navigation
7. Overlay Navigation
8. Images
9. Content Grids
10. Feature Columns
11. Form Elements
12. Buttons
13. Dropdowns
14. Lists
15. Menu Lists
16. Timeline
17. Tabs
18. Accordion
19. Tables
20. Pricing Tables
21. Boxes
22. Labels
23. Charts
24. Range Slider
25. Video & Audio
26. Dividers
27. Typography
28. <PERSON><PERSON>uo<PERSON>
29. Breadc<PERSON>b
30. Code & Pre
31. Font Icons
32. Color & Shadow Classes
33. Animation
34. Media Queries
-------------------------------------------------------------------*/

/*------------------------------------------------------------------
[1. Common]
*/
html{
	font-size: 62.5%;
}
html, body {
	max-width: 100%;
}
body {
	padding: 0;
	margin: 0;
	background: #ffffff;
	color: #666666;
 	font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
 	font-weight: normal;
	font-style: normal;
	font-size: 1.4rem;
	line-height: 1.8;
	overflow-x: hidden !important;
    overflow-y: scroll;
    -webkit-text-size-adjust: none;
    -webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
*,
*:before,
*:after {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
a,
button,
input,
select,
textarea {
	touch-action: manipulation;
}
button,
.button,
input,
select{
	line-height: 1.4;
}
.page-fade-reset{
	opacity: 1;
	-webkit-transition-duration: 0ms;
			transition-duration: 0ms;
}
.page-fade-out{
	opacity: 0;
	-webkit-transition-property: opacity;
			transition-property: opacity;
	-webkit-transition-duration: 500ms;
			transition-duration: 500ms;
	-webkit-backface-visibility: hidden;
}
.scroll-to-top{
	opacity: 0;
	visibility: hidden;
	-webkit-transition-property: all;
			transition-property: all;
	-webkit-transition-duration: 500ms;
			transition-duration: 500ms;
}
.scroll-to-top.active{
	opacity: 1;
	visibility: visible;
}
.scroll-to-top.fixed{
	width: 4rem;
	height: 4rem;
	line-height: 4rem;
	border-radius: 50%;
	background-color: #333333;
	content: "\e63e";
	text-align: center;
	position: fixed;
	right: 3rem;
	bottom: 3rem;
	top: auto;
	z-index: 999;
}
.scroll-to-top.fixed a{
	color: #ffffff;
}

/*------------------------------------------------------------------
[2. Timber Grid]
*/

/* Wrapper */
.wrapper{
	width: 100%;
	margin-right: auto;
	margin-left: auto;
	overflow: hidden;
	-webkit-backface-visibility: hidden;
}
.wrapper-inner{
	max-width: 100%;
	margin-right: auto;
	margin-left: auto;
	overflow: hidden;
}
body.boxed .wrapper-inner{
	max-width: 114rem;
}
.row{
	width: 100%;
	max-width: 114rem;
	margin-right: auto;
	margin-left: auto;
	margin-top: 0;
	margin-bottom: 0;
}
/* ie fix */
.ie-browser .row{
	max-width: 1140px;
}
.row.full-width{
	max-width: 100%;
}
.row .row{
	width: auto;
	margin-right: -1.5rem;
	margin-left: -1.5rem;
}
.row:before, 
.row:after{
	content: ".";
	display: block;
	height: 0;
	overflow: hidden;
}
.row:after{
	clear: both;
}
.column,
[class*="content-grid"] .grid-item{
	width: 100%;
	padding-left: 1.5rem;
	padding-right: 1.5rem;
	float: left;
}
.column > *:first-child{
	margin-top: 0;
}
.row.collapse > .column{
	padding-left: 0;
	padding-right: 0;
}
.row.full-width.collapse .row {
	margin: auto;
}

/* Flex rows */
.row.flex.v-align-middle{
	height: 100%;
	display: flex;
	align-items: center;
	flex-wrap: nowrap;
}
.row.flex.v-align-middle > .column >  div{
	width: initial;
}
.row.flex.v-align-top{
	align-items: flex-start;
}
.row.flex.v-align-bottom{
	align-items: flex-end;
}
/* ie9,10 fix */
.ie-browser:not(.webkit) .row.flex.v-align-middle{
	height: 100%;
	display: table;
	align-items: center;
	flex-wrap: nowrap;
}
.ie-browser:not(.webkit) .row.flex.v-align-middle > .column{
	display: table-cell;
	vertical-align: middle;
	float: none;
}
.ie-browser:not(.webkit) .row.flex.v-align-top{
	vertical-align: top;
}
.ie-browser:not(.webkit) .row.flex.v-align-bottom{
	vertical-align: bottom;
}

/*Flex columns*/
.row.flex{
	height: auto;
	display: flex;
	flex-wrap: wrap;
	align-items: initial;
}
.row.flex > .v-align-middle,
.row.flex.boxes > .column,
.row.flex.boxes > .v-align-middle,
.row.flex > .v-align-middle > .box{
	height: auto;
	display: inherit;
}
.row.flex > .v-align-top,
.row.flex.boxes > .v-align-top,
.row.flex > .v-align-top > .box{
	align-self: flex-start;
}
.row.flex > .v-align-bottom,
.row.flex.boxes > .v-align-bottom,
.row.flex > .v-align-bottom > .box{
	align-self: flex-end;
}
.row.flex > .column > .box > div {
	margin-top: auto;
	margin-bottom: auto;
}
.row.flex > .v-align-top > .box > div{
	margin-top: 0;
	margin-bottom: auto;
}
.row.flex > .v-align-bottom > .box > div{
	margin-top: auto;
	margin-bottom: 0;
}
.row.flex > .column > div{
	flex-direction: column;
}
.row.flex .row.flex{
	flex-direction: row;
}
@media only screen and (min-width: 768px){
	.row.flex .v-align-middle > *:not(.box),
	.row.flex.boxes .v-align-middle > div > *:not(.box){
		width: 100%;
		margin-top: auto;
		margin-bottom: auto;
	}
	/* ie10 fix */
	.ie-browser:not(.webkit) .flex > .column{
		align-items: center;
	}
}
.row.flex:before,
.row.flex:after{
	content: "";
	display: none;
}

/*Row padding*/
.row.small{
	padding: 3rem 0;
}
.row.medium{
	padding: 5rem 0;
}
.row.large{
	padding: 7rem 0;
}
.row.xlarge{
	padding: 9rem 0;
}

/*Columns Widths*/
.width-1	{width: 8.33333%;}
.width-2	{width: 16.66667%;}
.width-3	{width: 25%;}
.width-4	{width: 33.33333%;}
.width-5	{width: 41.66667%;}
.width-6	{width: 50%;}
.width-7	{width: 58.33333%;}
.width-8	{width: 66.66667%;}
.width-9	{width: 75%;}
.width-10	{width: 83.33333%;}
.width-11	{width: 91.66667%;}
.width-12	{width: 100%;}

/*Push - Pull*/
.column[class*='push-'],
.column[class*='pull-']{
	position: relative;
}
.push-1		{left: 8.33333%;}
.push-2		{left: 16.66667%;}
.push-3		{left: 25%;}
.push-4		{left: 33.33333%;}
.push-5		{left: 41.66667%;}
.push-6		{left: 50%;}
.push-7		{left: 58.33333%;}
.push-8		{left: 66.66667%;}
.push-9		{left: 75%;}
.push-10	{left: 83.33333%;}
.push-11	{left: 91.66667%;}

.pull-1		{right: 8.33333%;}
.pull-2		{right: 16.66667%;}
.pull-3		{right: 25%;}
.pull-4		{right: 33.33333%;}
.pull-5		{right: 41.66667%;}
.pull-6		{right: 50%;}
.pull-7		{right: 58.33333%;}
.pull-8		{right: 66.66667%;}
.pull-9		{right: 75%;}
.pull-10	{right: 83.33333%;}
.pull-11	{right: 91.66667%;}

/*Offset*/
.offset-1	{margin-left: 8.33333%;}
.offset-2	{margin-left: 16.66667%;}
.offset-3	{margin-left: 25%;}
.offset-4	{margin-left: 33.33333%;}
.offset-5	{margin-left: 41.66667%;}
.offset-6	{margin-left: 50%;}
.offset-7	{margin-left: 58.33333%;}
.offset-8	{margin-left: 66.66667%;}
.offset-9	{margin-left: 75%;}
.offset-10	{margin-left: 83.33333%;}
.offset-11	{margin-left: 91.66667%;}

/*Clear Margins*/
.alpha{padding-left: 0;}
.omega{padding-right: 0;}

/*Clearfix*/
.clear {
	width: 0;
	height: 0;
	display: block;
	clear: both;
	overflow: hidden;
	visibility: hidden;
}
.clearfix:before,
.clearfix:after,
.row:before,
.row:after{
	height: 0;
	content: ".";
	display: block;
	overflow: hidden;
}
.clearfix:after,
.row:after{
	clear: both;
}

/*------------------------------------------------------------------
[3. Helper Classes]
*/
.left{
	text-align: left;
}
.right{
	text-align: right;
}
.center{
	text-align: center;
}
.text-break{
	overflow-wrap: break-word;
	word-wrap: break-word;
	-ms-word-break: break-all;
	word-break: break-word;
	-webkit-hyphens: auto;
		-ms-hyphens: auto;
		-moz-hyphens: auto;
 			hyphens: auto;
}
.text-truncate{
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.pull-left{
	float: left !important;
}
.pull-right{
	float: right !important;
}
.flex{
	width: 100%;
	display: flex;
	align-items: center;
}
.v-align-top{
	align-self: flex-start !important;
	vertical-align: top !important;
}
.v-align-bottom{
	align-self: flex-end !important;
	vertical-align: bottom !important;
}
.hide{
	display: none !important;
	visibility: hidden !important;
}
.show{
	display: block !important;
	visibility: visible !important;
}
.mobile .hide-on-mobile,
.mobile .hide-on-mobile *{
	display: none !important;
	visibility: visible !important;
}
.inline-block,
[class*="border-"].inline-block,
[class*="bkg-"].inline-block{
	width: auto !important;
	display: inline-block !important;
}
.no-margins{
	margin: 0 !important;
}
.no-margin-top{
	margin-top: 0 !important; 
}
.no-margin-right{
	margin-right: 0 !important; 
}
.no-margin-bottom{
	margin-bottom: 0 !important;
}
.no-margin-left{
	margin-left: 0 !important;
}
.no-padding{
	padding: 0 !important;
}
.no-padding-top{
	padding-top: 0 !important;
}
.no-padding-right{
	padding-right: 0 !important; 
}
.no-padding-bottom{
	padding-bottom: 0 !important;
}
.no-padding-left{
	padding-left: 0 !important; 
}
.overflow-visible{
	overflow: visible !important;
}
.no-scroll{
	overflow: hidden !important;
}
.clear-height{
	height: auto !important;
}
.full-width{
	width: 100% !important;
}

/*------------------------------------------------------------------
[4. Prototype Classes]
*/
.prototype{
	font-size: 1.1rem;
	line-height: 3.5rem;
}
.row.prototype .column:nth-child(odd){
	background: #f9f9f9;
}
.row.prototype .column:nth-child(even){
	background: #eee;
}

/*------------------------------------------------------------------
[5. Main Navigation]
*/
.nav-bar{
	width: 100%;
	min-height: 5rem;
}
.nav-bar-inner{
	position: relative;
}
.nav-bar .logo{
	height: 5rem;
	line-height: 5rem;
	float: left;
}
.nav-bar .logo{
	margin-right: 3rem;
}
.nav-bar .logo img{
	margin-top: -4px;
}
.nav-bar .logo-right{
	margin-left: 3rem;
	margin-right: 0;
	float: right;
}
.nav-bar .logo.logo-center{
	margin: 0 auto;
	float: none;
}

/* Nav Alignment */
.navigation.nav-left{
	float: left !important;
}
.navigation.nav-left > ul > li:first-child > a{
	padding-left: 0;
	margin-left: 0; 
}
.navigation.nav-right{
	float: right !important;
}
.navigation.nav-right > ul > li:last-child > a{
	padding-right: 0;
	margin-right: 0;
}
.navigation.nav-center{
	float: none !important;
	display: block;
}

/* Multiple Nav Blocks Margins & Padding */
.navigation.nav-left{
	margin-right: 2.5rem;
}
.navigation.nav-right{
	margin-left: 2.5rem;
}
.navigation:last-child{
	margin: 0;
}

/* Nav Buttons and Dropdowns */
.navigation .dropdown .button{
	width: auto;
}
.navigation .button [class*="icon-"]{
	margin-right: 0;
}

/* Nav Structure */
.navigation ul,
.navigation .dropdown-list{
	padding: 0;
	margin: 0;
	list-style: none;
}
.navigation.nav-center > ul{
	text-align: center;
}
.navigation ul li{
	margin: 0;
	float: left;
	position: relative;
}
.navigation ul li.contains-mega-sub-menu{
	position: inherit;
}
.navigation.nav-center > ul > li{
	margin-left: -0.4rem;
	float: none;
	display: inline-block !important;
	vertical-align: top;
}

/* Nav Styling */
.navigation > ul > li > a{
	padding: 0 1.5rem;
	background-color: rgba(0,0,0,0);
	line-height: 5rem;
}
.navigation ul li > a:not(.button),
.navigation .dropdown-list:not(.custom-content) li a{
	font-size: 1.1rem;
	color: #878787; 
	text-decoration: none;
	display: block;
	word-wrap: break-word;
}
.navigation ul li > a:not(.button):hover,
.navigation .dropdown-list li > a:not(.button):hover{
	background: none;
	color: #ffffff;
}
.navigation ul li:hover > .sub-menu{
	visibility: visible;
	opacity: 1;
}
.navigation ul li.current > a{
	color: #ffffff;
}
.navigation ul li.current > a:hover{
	color: #ffffff;
}

/* Sub Menus */
.navigation .sub-menu,
.navigation .dropdown-list{
	width: 18rem;
	text-align: left;
	line-height: 1.8;
	background: #222222;
	background: rgba(34,34,34,1);
	box-shadow: 0 0.2rem 0.2rem rgba(0,0,0,.3);
	border: none;
	border-radius: 0;
	opacity: 0;
	visibility: hidden;
	position: absolute;
	z-index: 101;
}
.navigation .sub-menu li,
.navigation .dropdown-list li{
	background: none;
	border-bottom: 1px solid #303030;
	float: none;
}
.navigation .dropdown-list.custom-content,
.navigation .sub-menu.custom-content{
	width: auto;
	min-width: 25rem;
}
.navigation .dropdown-list.custom-content,
.navigation .sub-menu.custom-content .custom-content-inner{
	padding: 2rem;
}
.navigation .sub-menu.custom-content ul{
	padding: 0 2rem 2rem 2rem;
}
.navigation .sub-menu.custom-content ul:first-child{
	padding-top: 2rem;
}
.navigation .sub-menu.custom-content ul[class*="bkg-"]{
	padding: 2rem;
}
.navigation .custom-content li:last-child *:last-child{
	margin-bottom: 0;
}

/* Sub Menu Positions */
.navigation > ul > li.sub-menu-left .sub-menu{
	left: 0;
}
.navigation > ul > li:last-child > .sub-menu,
.navigation > ul > li.sub-menu-right .sub-menu,
.navigation .dropdown.pull-right .dropdown-list{
	right: 0;
}
.navigation .sub-menu ul{
	top: 0;
	left: 18rem;
}
.navigation > ul > li:last-child > .sub-menu ul,
.navigation > ul > li.sub-menu-right .sub-menu ul{
	right: 18rem;
	left: auto;
}

/* Sub Menu Links */
.navigation .sub-menu:not(.custom-content) li a,
.navigation .dropdown-list:not(.custom-content) li a{
	padding: 1.2rem 1.5rem;
}
.navigation .sub-menu:not(.custom-content) li a,
.navigation .dropdown-list:not(.custom-content) li a{
	color: #666666;
}
.navigation .sub-menu:not(.custom-content) li:last-child,
.navigation .dropdown-list:not(.custom-content) li:last-child{
	border-bottom: none;
}

/* Mega Sub Menu */
.navigation .mega-sub-menu{
	height: auto;
	padding: 2rem 0 0 0;
	margin-left: 0;
	margin-right: 0; 
	line-height: 1.8;
	background: #222222;
	background: rgba(34,34,34,1);
	box-shadow: 0 0.2rem 0.2rem rgba(0,0,0,.3);
	opacity: 0;
	overflow: hidden;
	visibility: hidden;
	display: flex;
	flex-wrap: wrap;
	position: absolute;
	left: 1.5rem;
	right: 1.5rem;
	z-index: 101;
}
.mega-sub-menu.row:before, 
.mega-sub-menu.row:after {
	display: none;
	content: "";
}
.navigation .mega-sub-menu > li{
	margin-bottom: 2rem;
}
.navigation .mega-sub-menu > li:not(.column){
	width: 33.33333%;
}
.navigation .mega-sub-menu > li{
	padding: 0 2rem;
	text-align: left;
	float: left;
	border-right: 1px solid #303030;
}
.navigation .mega-sub-menu > li > a:not(.button){
	background: none;
	font-weight: bold;
}
.navigation .mega-sub-menu > li:hover > a:not(.button){
	background: none;
}
.navigation .mega-sub-menu > li:last-child{
	border-right: none;
}
/* Mega Menu Link Parent */
.navigation .mega-sub-menu > li > a:not(.button){
	font-size: 1.2rem;
	line-height: 1.8;
	text-transform: uppercase;
}
.navigation .mega-sub-menu ul li p{
	font-size: 1.2rem;
	margin-bottom: 0;
}
.navigation .mega-sub-menu ul li p,
.navigation .mega-sub-menu > li > a:not(.button){
	padding: 0rem 1.5rem 0.7rem 1.5rem;
}
.navigation .mega-sub-menu ul li .title{
	padding: 0rem 1.5rem;
}
/* Mega Menu Child Links */
.navigation .mega-sub-menu ul li > a:not(.button){
	padding: 0.6rem 1.5rem;
}
.navigation .mega-sub-menu ul li.current > a:not(.button){
	color: #ffffff;
}
.navigation .mega-sub-menu ul li:hover > a:not(.button){
	background: #333333;
}
.navigation .mega-sub-menu li ul > li{
	float: none;
}
.navigation .mega-sub-menu li.content-column ul li{
	padding: 0.6rem 1.5rem;
}
.navigation .mega-sub-menu li.content-column ul li > a:not(.button){
	padding: 0;
}
.navigation ul li:hover > .mega-sub-menu{
	visibility: visible;
	opacity: 1;
}

/* Sub and Mega Menu Indication */
.navigation.sub-menu-indicator > ul > li.contains-sub-menu > a:after,
.navigation.sub-menu-indicator > ul > li.contains-mega-sub-menu > a:after,
.navigation .sub-menu li.contains-sub-menu > a:after{
	font-family: 'icomoon';
	font-size: inherit;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: inherit;
	display: inline-block;
	position: absolute;
}
.navigation.sub-menu-indicator > ul > li.contains-sub-menu,
.navigation.sub-menu-indicator > ul > li.contains-mega-sub-menu{
	padding-right: 1.2rem;
}
.navigation.sub-menu-indicator > ul > li.contains-sub-menu > a:after,
.navigation.sub-menu-indicator > ul > li.contains-mega-sub-menu > a:after{
	margin-left: 0.5rem;
	content: "\e63e";
}
.navigation .sub-menu li.contains-sub-menu > a{
	padding-right: 2.2rem;
}
.navigation .sub-menu li.contains-sub-menu > a:after{
	right: 0.8rem;  
	content: "\e640";
}

/* Menu Animation Attributes */
.navigation .sub-menu,
.navigation .mega-sub-menu{
	-webkit-transition-property: -webkit-transform, opacity, visibility, border-color;
			transition-property: transform, opacity, visibility, border-color;
	-webkit-transition-duration: 400ms;
			transition-duration: 400ms;
}
.navigation ul li a{
	-webkit-transition-property: background, border-color, color, line-height;
			transition-property: background, border-color, color, line-height;
	-webkit-transition-duration: 500ms;
			transition-duration: 500ms;
}

/* Aux Nav Toggle Button*/
.aux-navigation{
	display: none;
}
.navigation-show,
.navigation-hide{
	width: 4.4rem;
	height: 4.4rem;
	text-align: center;
	float: right;
}
.navigation-show a,
.navigation-hide a{
	height: 4.4rem;
	text-align: center;
	display: block;
}
.navigation-show span,
.navigation-hide span{
	width: 100%;
	line-height: 4.4rem;
}
.navigation-show a{
	color:#666666;
}
.navigation-show a:hover{
	color:#fff;
}
.navigation-hide{
	position: relative;
	z-index: 10;
}
.navigation-hide a{
	color: #666666;
}
.navigation-hide a:hover{
	color:#fff;
}

/* Mobile */
.mobile .logo.logo-center{
	float: left;
}
.mobile header .navigation:not(.secondary-navigation),
.mobile header .primary-nav-column{
	display: none !important;
}
.mobile header .logo-column,
.mobile header .secondary-nav-column{
	width: auto;
}
.mobile header .logo-column{
	float: left;
}
.mobile header .secondary-nav-column{
	float: right;
}

/* Mobile Access */
.mobile .aux-navigation{
	display: table !important;
	visibility: visible !important;
}

/*------------------------------------------------------------------
[6. Side Navigation]
*/

/* Associated Wrapper Class */
.reveal-side-navigation > div:after {
	position: absolute;
	top: 0;
	right: 0;
	width: 0;
	height: 0;
	background: rgba(0,0,0,0);
	content: '';
	opacity: 0;
	z-index: 110;
	-webkit-transition: opacity 300ms, width 0ms, height 0ms;
			transition: opacity 300ms, width 0ms, height 0ms;
}
.inactive.reveal-side-navigation > div:after {
	width: 100%;
	height: 100%;
	opacity: 1;
	-webkit-transition: opacity 300ms;
			transition: opacity 300ms;
}

/* Associated Wrapper Animation */
.element-reveal-left{
	-webkit-transform: translate3d(35rem,0,0);
			transform: translate3d(35rem,0,0);
}
.element-reveal-right{
	-webkit-transform: translate3d(-35rem,0,0);
			transform: translate3d(-35rem,0,0);
}

/* Side Navigation Wrapper */
.side-navigation-wrapper,
.side-navigation-inner{
	width: 35rem;
}
.side-navigation-wrapper{
	height: 100%;
	padding: 0;
	background: #292929;
	box-shadow: inset -7px 0 9px -7px rgba( 0, 0, 0, 0.6 );
	overflow-y: scroll;
	visibility: hidden;
	position: fixed;
	left: 0;
	right: auto;
	-webkit-transform: translate3d(-100%,0,0);
	        transform: translate3d(-100%,0,0);
	-webkit-backface-visibility: hidden;
}

.side-navigation-wrapper.active{
	visibility: visible;
}
.side-navigation-wrapper.enter-right{
	text-align: left;
	box-shadow: inset 7px 0 9px -7px rgba( 0, 0, 0, 0.6 );
	right: 0;
	left: auto;
	-webkit-transform: translate3d(100%,0,0);
	        transform: translate3d(100%,0,0);
}
.side-navigation-wrapper.no-scrollbar{
	overflow: hidden;
}
.no-scrollbar .side-navigation-scroll-pane{
	height: 100%;
	overflow-x: hidden;
	overflow-y: scroll;
	position: absolute;
	left: 0;
}
.no-scrollbar .side-navigation-scroll-pane::-webkit-scrollbar{
	display: none;
}
.side-navigation-inner{
	min-height: 100%;
	position: relative;
}

/* Side Navigation Animation Classes */
/* no transition */
.side-navigation-wrapper.no-transition-reset{
	-webkit-transform: translate3d(0,0,0);
			transform: translate3d(0,0,0);
}
/* slide in */
.side-navigation-wrapper.slide-in-reset{
	z-index: 104;
}
.side-navigation-wrapper.slide-in{
	-webkit-transform: translate3d(0,0,0);
			transform: translate3d(0,0,0);
}
.element-reveal-left.slide-in,
.element-reveal-right.slide-in{
	-webkit-transform: translate3d(0,0,0);
			transform: translate3d(0,0,0);
}

/* push in */
.side-navigation-wrapper.push-in-reset{
	-webkit-transform: translate3d(-60%,0,0);
			transform: translate3d(-60%,0,0);
}
.side-navigation-wrapper.enter-right.push-in-reset{
	-webkit-transform: translate3d(60%,0,0);
	        transform: translate3d(60%,0,0);
}
.side-navigation-wrapper.push-in,
.side-navigation-wrapper.enter-right.push-in{
	-webkit-transform: translate3d(0,0,0);
			transform: translate3d(0,0,0);
}

/* reveal */
.side-navigation-wrapper.reveal-reset,
.side-navigation-wrapper.enter-right.reveal-reset{
	-webkit-transform: translate3d(0,0,0);
			transform: translate3d(0,0,0);
}

/* scale in */
.side-navigation-wrapper.scale-in-reset,
.side-navigation-wrapper.enter-right.scale-in-reset{
	-webkit-transform: translate3d(0,0,0) scale(0.9);
			transform: translate3d(0,0,0) scale(0.9);
}
.side-navigation-wrapper.scale-in,
.side-navigation-wrapper.enter-right.scale-in{
	-webkit-transform: translate3d(0,0,0) scale(1);
			transform: translate3d(0,0,0) scale(1);
}

/* Animation Properties */
.side-navigation-wrapper,
.reveal-side-navigation{
	-webkit-transition: -webkit-transform 550ms, visibility 0ms;
			transition: transform 550ms, visibility 0ms;
}

/* Side navigation fallback for
 non CSS3 supporting browsers */
.side-navigation-wrapper.hide{
	visibility: hidden;
	left: -35rem;
}
.side-navigation-wrapper.enter-right.hide{
	right: -35rem;
}
.element-show-left{
	left: 35rem;
}
.element-show-right{
	left: -35rem;
}
.element-show-left,
.element-show-right{
	position: relative !important;
}

/* Menu */
.side-navigation{
	width: 100%;
}
.side-navigation:before, 
.side-navigation:after{
	content: ".";
	display: block;
	height: 0;
	overflow: hidden;
	clear: both;
}
.side-navigation ul{
	padding: 0;
	margin: 0;
	list-style: none;
}
.side-navigation ul li{
	width:100%;
	display:block;
	margin-bottom: 0;
}
.side-navigation > ul > li > a{
	padding: 1.3rem 4rem;
}
.side-navigation ul li a{
	font-size: 1.2rem;
	line-height: 1;
	color: #999999; 
	text-decoration: none;
	display: block;
	word-wrap: break-word;
	-webkit-transition-property: background, color;
			transition-property: background, color;
	-webkit-transition-duration: 300ms;
			transition-duration: 300ms;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.side-navigation ul li a:hover{
	color:#ffffff;
}
.side-navigation ul li.current > a{
	color:#ffffff;
	font-weight: bold;
}
.side-navigation ul li.current > a:hover{
	color:#ffffff;
}
.side-navigation .sub-menu:not(.custom-content) a{
	padding: 1.1rem 4rem 1.1rem 5.5rem;
}
.side-navigation.center .sub-menu:not(.custom-content) a{
	padding: 1.1rem 4rem 1.1rem 4rem;
}
.side-navigation .sub-menu:not(.custom-content) a{
	padding-left: 7rem;
}
.side-navigation:not(.center) .sub-menu .sub-menu{
	padding-left: 1.5rem;
}
.side-navigation  ul li:hover > .sub-menu{
	opacity: 1;
}
.side-navigation .sub-menu{
	overflow: hidden;
}
.side-navigation .sub-menu{
	height: 0;
	-webkit-transition-property: height;
			transition-property: height;
	-webkit-transition-duration: 300ms;
			transition-duration: 300ms;
}

/*------------------------------------------------------------------
[7. Overlay Navigation]
*/

.overlay-navigation-wrapper,
.overlay-navigation-inner{
	width: 100vw;
}
.overlay-navigation-wrapper{
	width: 100vw;
	height: 100%;
	min-height: 100%;
	background-color: rgba( 0, 0, 0, 0.8 );
	overflow-y: scroll;
	visibility: hidden;
	position: fixed;
	top: -100%;
	z-index: 101;
}
.overlay-navigation-wrapper.active{
	visibility: visible;
	top: 0 !important;
}
.overlay-navigation-wrapper.no-scrollbar{
	overflow: hidden;
}
.overlay-navigation-scroll-pane{
	height: 100%;
}
.no-scrollbar .overlay-navigation-scroll-pane{
	overflow-x: hidden;
	overflow-y: scroll;
	position: absolute;
	left: 0;
}
.no-scrollbar .overlay-navigation-scroll-pane::-webkit-scrollbar{
	display: none;
}
.overlay-navigation-inner{
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	position: relative;
}

/* Overlay Navigation Animation Classes */
/* no transition */
.overlay-navigation-wrapper.no-transition-reset{
	-webkit-transform: translate3d(0,0,0);
			transform: translate3d(0,0,0);
}
/* fade in */
.overlay-navigation-wrapper.fade-in-reset{
	opacity: 0;
}
.overlay-navigation-wrapper.fade-in{
	opacity: 1;
}
/* slide in */
.overlay-navigation-wrapper.slide-in-reset{
	-webkit-transform: translate3d(-100%,0,0);
			transform: translate3d(-100%,0,0);
	opacity: 0;
}
.overlay-navigation-wrapper.enter-top.slide-in-reset{
	-webkit-transform: translate3d(0,-100%,0);
			transform: translate3d(0,-100%,0);
	opacity: 0;
}
.overlay-navigation-wrapper.enter-right.slide-in-reset{
	-webkit-transform: translate3d(100%,0,0);
			transform: translate3d(100%,0,0);
	opacity: 0;
}
.overlay-navigation-wrapper.enter-bottom.slide-in-reset{
	-webkit-transform: translate3d(0,100%,0);
			transform: translate3d(0,100%,0);
	top: 100%;
	opacity: 0;
}
.overlay-navigation-wrapper.slide-in,
.overlay-navigation-wrapper.enter-top.slide-in,
.overlay-navigation-wrapper.enter-right.slide-in,
.overlay-navigation-wrapper.enter-bottom.slide-in{
	-webkit-transform: translate3d(0,0,0);
			transform: translate3d(0,0,0);
	opacity: 1;
}

/* scale in */
.overlay-navigation-wrapper.scale-in-reset{
	-webkit-transform: translate3d(0,0,0) scale(0.8);
			transform: translate3d(0,0,0) scale(0.8);
	opacity: 0;
}
.overlay-navigation-wrapper.scale-in{
	-webkit-transform: translate3d(0,0,0) scale(1);
			transform: translate3d(0,0,0) scale(1);
	opacity: 1;
}

/* Animation Properties */
.overlay-navigation-wrapper{
	-webkit-transition: -webkit-transform 550ms, opacity 550ms;
			transition: transform 550ms, opacity 550ms;
}

/* Menu */
.overlay-navigation{
	max-width: 30rem;
	margin-left: auto;
	margin-right: auto;
}
.overlay-navigation:before, 
.overlay-navigation:after{
	content: ".";
	display: block;
	height: 0;
	overflow: hidden;
	clear: both;
}
.overlay-navigation ul{
	padding: 0;
	margin: 0;
	list-style: none;
}
.overlay-navigation ul li{
	width: 100%;
	display: block;
	margin-bottom: 0;
}
.overlay-navigation > ul > li > a{
	padding: 0.6rem 0;
}
.overlay-navigation ul li a{
	font-size: 1.2rem;
	line-height: 1.5;
	color: #878787; 
	text-decoration: none;
	display: block;
	word-wrap: break-word;
	-webkit-transition-property: background, color;
			transition-property: background, color;
	-webkit-transition-duration: 300ms;
			transition-duration: 300ms;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.overlay-navigation ul li a:hover{
	color: #ffffff;
}
.overlay-navigation ul li.current > a{
	color:#ffffff;
	font-weight: bold;
}
.overlay-navigation ul li.current > a:hover{
	color:#ffffff;
}
.overlay-navigation .sub-menu:not(.custom-content) a{
	padding: 1.1rem 4rem 1.1rem 5.5rem;
}
.overlay-navigation.center .sub-menu:not(.custom-content) a{
	padding: 1.1rem 4rem 1.1rem 4rem;
}
.overlay-navigation .sub-menu a{
	color: #666666;
}
.overlay-navigation .sub-menu .sub-menu a{
	padding-left: 7rem;
}
.overlay-navigation  ul li:hover > .sub-menu{
	opacity: 1;
}
.overlay-navigation .sub-menu{
	overflow: hidden;
}
.overlay-navigation .sub-menu{
	height: 0;
	-webkit-transition-property: height, border-color;
			transition-property: height, border-color;
	-webkit-transition-duration: 300ms;
			transition-duration: 300ms;
}

/*------------------------------------------------------------------
[8. Images]
*/
img{
	max-width: 100%;
	height: auto;
	display: inline-block;
	vertical-align: middle;
	border: none;
	border-radius: inherit;
	outline: none;
}

/*Alignment*/
img.pull-left,
.thumbnail.pull-left{
	margin: 0 1.5rem 1rem 0;
}
img.pull-right,
.thumbnail.pull-right{
	margin: 0 0 1.5rem 1rem;
}

/* Thumbnail */
.thumbnail .background-image{
	width: 100%;
	height: 100%;
	display: block;
	background-position: center center;
	-webkit-background-size: cover;
	background-size: cover;
	background-repeat: no-repeat;
}
.thumbnail{
	margin-bottom: 3rem;
	position: relative;
	overflow: hidden;
	display: inline-block;
	vertical-align: bottom;
}
.thumbnail.rounded,
img.rounded{
	border-radius: 0.3rem;
}
.thumbnail.circle,
img.circle{
	border-radius: 50%;
}
.thumbnail > span,
.thumbnail > a{
	width: 100%;
	height:100%;
	border-radius: inherit;
	float: left;
}
.thumbnail video{
	max-width: 100%;
	height: auto;
	position: relative;
	z-index: 1;
}
.thumbnail video + img{
	position: absolute;
	top: 0;
	z-index: 0;
}
/* ff fix for flicker on hover */
.thumbnail:hover {
	transform: none !important;
	transition: none !important;
}
/* Thumbs with background images */
.thumbnail.background-image-container{
	width: 100%;
	min-height: 30rem;
}

/* Captions */
.caption-below{
	width: 100%;
	padding: 1rem 1.5rem;
	font-size: 1.2rem;
	font-style: italic;
	border: 1px solid #eeeeee;
	float: left;
}
.thumbnail .caption-over-outer{
	width: 100%;
	height: 100%;
	padding: 3rem;
	display: flex;
	align-items: center;
	position: absolute;
	bottom: 0;
	z-index: 2;
}
.thumbnail .caption-over-inner{
	width: 100%;
	height: auto;
}
.thumbnail .caption-over-inner.v-align-top{
	align-self: flex-start;
}
.thumbnail .caption-over-inner.v-align-bottom{
	align-self: flex-end;
}
.thumbnail .caption-over-inner *{
	flex: none;
}
.thumbnail .caption-over-inner *:first-child{
	margin-top: 0;
}
.thumbnail .caption-over-inner *:last-child{
	margin-bottom: 0;
}
/* ie9,10 fix */
.ie-browser:not(.webkit) .thumbnail .caption-over-outer{
	width: 100%;
	height: 100%;
	padding: 2rem;
	display: table;
	opacity: 0;
	position: absolute;
	bottom: 0;
	z-index: 2;
}
.ie-browser:not(.webkit) .thumbnail .caption-over-inner{
	width: 100%;
	height: 100%;
	display: table-cell;
	vertical-align: middle;
}

/* Overlays */
.thumbnail > span,
.thumbnail > a{
	width: 100%;
	height: 100%;
	display: block;
	outline: none;
	border: none;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
}
.overlay-info{
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	color: #fff;
	border: none;
	border-radius: inherit;
	background: #000;
	background: rgba(0,0,0,0.5);
	text-align: center;
	opacity: 0;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1; 
}
a.overlay-info:hover{
	color: #fff;
}
.overlay-info > span,
.overlay-info > div{
	width: 100%;
	height: 100%;
	padding: 3rem;
	font-size: 1.2rem;
	font-style: normal;
	display: flex;
	align-items: center;
}
.overlay-info > span > span,
.overlay-info > div > div{
	width: 100%;
	height: auto;
}
/* ie9,10 fix */
.ie-browser:not(.webkit) .overlay-info > span,
.ie-browser:not(.webkit) .overlay-info > div{
	width: 100%;
	height: 100%;
	padding: 1.5rem;
	font-size: 1.2rem;
	font-style: normal;
	display: table;
}
.ie-browser:not(.webkit) .overlay-info > span > span,
.ie-browser:not(.webkit) .overlay-info > div > div{
	display: table-cell;
	text-align: center;
	vertical-align: middle;
}

/* Background Images */
.fill-background{
	background-repeat: no-repeat;
	background-position: center center;
	-webkit-background-size: cover;
			background-size: cover;
}

/* Horizontal Alignment */
.overlay-info.left > span > span{
	text-align: left;
}
.overlay-info.right > span > span{
	text-align: right;
}

/* Vertical Alignment */
.thumbnail .caption-over-inner.v-align-top,
.overlay-info.v-align-top > span > span,
.overlay-info.v-align-top > div > div{
	align-self: flex-start;
	vertical-align: top;
}
.thumbnail .caption-over-inner.v-align-bottom,
.overlay-info.v-align-bottom > span > span,
.overlay-info.v-align-bottom > div > div{
	align-self: flex-end;
	vertical-align: bottom;
}

/* Animation Classes */
.thumbnail .overlay-info{
	width: 100.1%;
	opacity: 0;
}
.thumbnail:hover .overlay-info{
	opacity: 1;
}
.thumbnail .overlay-info{
	-webkit-transform: translateZ(0);
            transform: translateZ(0);
    -webkit-backface-visibility: hidden;
}
.thumbnail:hover .overlay-info,
.thumbnail:hover img,
.thumbnail:hover .background-image{
	-webkit-transform: translate3d(0,0,0) rotate3d(1, 1, 1, 0deg) scale3d(1, 1, 1);
            transform: translate3d(0,0,0) rotate3d(1, 1, 1, 0deg) scale3d(1, 1, 1);
}
.overlay-fade-out .overlay-info,
.overlay-fade-img-scale-in .overlay-info,
.overlay-fade-img-scale-out .overlay-info{
	opacity: 1;
}
.overlay-fade-out:hover .overlay-info,
.overlay-fade-img-scale-in:hover .overlay-info,
.overlay-fade-img-scale-out:hover .overlay-info{
	opacity: 0;
}
.overlay-slide-in-top .overlay-info{
	opacity: 0;
    -webkit-transform: translate3d(0,-100%,0);
            transform: translate3d(0,-100%,0);
}
.overlay-slide-in-right .overlay-info{
	opacity: 0;
	-webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0);
}
.overlay-slide-in-bottom .overlay-info{
	opacity: 0;
    -webkit-transform: translate3d(0,100%,0);
            transform: translate3d(0,100%,0);
}
.overlay-slide-in-left .overlay-info{
	opacity: 0;
    -webkit-transform: translate3d(-100%,0,0);
            transform: translate3d(-100%,0,0);
}
.overlay-scale-in .overlay-info{
	opacity: 0;
  	-webkit-transform: scale3d(0.1,0.1,1);
            transform: scale3d(0.1,0.1,1);
}
.overlay-rotate-in .overlay-info{
	opacity: 0;
	-webkit-transform: rotate3d(0.1, 0.1, 1, -180deg);
            transform: rotate3d(0.1, 0.1, 1, -180deg);
}
.overlay-img-slide-right .overlay-info{
	opacity: 1;
	-webkit-transform: translate3d(-100%,0,0);
            transform: translate3d(-100%,0,0);
}
.overlay-img-slide-right:hover img,
.overlay-img-slide-right:hover .background-image{
	-webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0);
}
.overlay-img-slide-left .overlay-info{
	opacity: 1;
	-webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0);
}
.overlay-img-slide-left:hover img,
.overlay-img-slide-left:hover .background-image{
	-webkit-transform: translate3d(-100%,0,0);
            transform: translate3d(-100%,0,0);
}
.overlay-img-slide-down .overlay-info{
	opacity: 1;
	-webkit-transform: translate3d(0,-100%,0);
            transform: translate3d(0,-100%,0);
}
.overlay-img-slide-down:hover img,
.overlay-img-slide-down:hover .background-image{
	-webkit-transform: translate3d(0,100%,0);
            transform: translate3d(0,100%,0);
}
.overlay-img-slide-up .overlay-info{
	opacity: 1;
	-webkit-transform: translate3d(0,100%,0);
            transform: translate3d(0,100%,0);
}
.overlay-img-slide-up:hover img,
.overlay-img-slide-up:hover .background-image{
	-webkit-transform: translate3d(0,-100%,0);
            transform: translate3d(0,-100%,0);
}
.img-scale-in:hover img,
.img-scale-in:hover .background-image,
.img-scale-out img,
.img-scale-out .background-image,
.overlay-fade-img-scale-out img,
.overlay-fade-img-scale-out .background-image,
.overlay-fade-img-scale-in:hover img,
.overlay-fade-img-scale-in:hover .background-image,
.overlay-img-scale-in:hover img,
.overlay-img-scale-in:hover .background-image{
	opacity: 1;
	-webkit-transform: scale3d(1.2,1.2,1);
            transform: scale3d(1.2,1.2,1);
}
.overlay-img-scale-in .overlay-info{
	-webkit-transform: scale3d(0.4,0.4,1);
            transform: scale3d(0.4,0.4,1);
}

/* Styles */
.thumbnail.shadow{
	-webkit-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
	   -moz-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
		    box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
}

/*------------------------------------------------------------------
[9. Content Grids]
*/

/* Grid Item*/
[class*="content-grid"] .grid-item{
	margin: 0 0 3rem;
	float: left;
}
[class*="content-grid"] .thumbnail{
	max-width: 100%;
	float: none;
}

/* Item Widths */
.content-grid-1 .grid-item{
	width: 100%;
}
.content-grid-2 .grid-item{
	width: 50%;
}
.content-grid-3 .grid-item{
	width: 33.33333%;
}
.content-grid-4 .grid-item{
	width: 25%;
}
.content-grid-5 .grid-item{
	width: 20%;
}
.content-grid-6 .grid-item{
	width: 16.66667%;
}

/* Marginless Grid */
[class*="content-grid-"].no-margins,
.row [class*="content-grid-"].no-margins{
	margin-right: 0 !important;
	margin-left: 0 !important;
}
[class*="content-grid-"].no-margins .grid-item{
	padding: 0 !important;
	margin: 0 !important;
}

/* Text Column */
.text-column{
	margin-bottom: 3rem;
}
.text-column > *:last-child{
	margin-bottom: 0;
}

/*------------------------------------------------------------------
[10. Feature Columns]
*/
.feature-column-group .feature-column{
	padding: 7rem;
	margin-bottom: 0;
}
.feature-column{
	margin-bottom: 3rem;
}
.feature-column > *:last-child{
	margin-bottom: 0;
}
.feature-icon{
	font-size: 3rem;
	margin-bottom: 2rem;
	display: inline-block;
	position: relative;
}
.feature-column.left .feature-icon{
	float: left;
}
.feature-text.left{
	padding-left: 5rem;
}
.feature-column.right .feature-text{
	padding-right: 5rem;
	padding-left: 0;
}
.feature-text *:first-child{
	margin-top: 0;
}

/* Sizes and Positions */
.feature-column.small .feature-icon{
	font-size: 2rem;
}
.feature-column.left.small .feature-text{
	padding-left: 4rem;
}
.feature-column.left.small .icon-boxed,
.feature-column.left.small .icon-circled{
	width: 5.8rem;
	height: 5.8rem;
	line-height: 5.8rem;
}
.feature-column.left.small .icon-boxed + .feature-text,
.feature-column.left.small .icon-circled + .feature-text{
	padding-left: 9rem;
}
.feature-column.right.small .feature-text{
	padding-right: 4rem;
	padding-left: 0;
}
.feature-column.right.small .icon-boxed + .feature-text,
.feature-column.right.small .icon-circled + .feature-text{
	padding-right: 9rem;
	padding-left: 0;
}
.feature-colum.mediumn .feature-icon{
	font-size: 3rem;
}
.feature-column.left.medium .feature-text{
	padding-left: 5rem;
}
.feature-column.medium .icon-boxed,
.feature-column.medium .icon-circled{
	width: 6.6rem;
	height: 6.6rem;
	line-height: 6.6rem;
}
.feature-column.left .icon-boxed + .feature-text,
.feature-column.left .icon-circled + .feature-text,
.feature-column.left.medium .icon-boxed + .feature-text,
.feature-column.left.medium .icon-circled + .feature-text{
	padding-left: 9.8rem;
}
.feature-column.right .icon-boxed + .feature-text,
.feature-column.right .icon-circled + .feature-text,
.feature-column.right.medium .icon-boxed + .feature-text,
.feature-column.right.medium .icon-circled + .feature-text{
	padding-right: 9.8rem;
	padding-left: 0;
}
.feature-column.large .feature-icon{
	font-size: 4rem;
}
.feature-column.left.large .feature-text{
	padding-left: 6rem;
}
.feature-column.large .icon-boxed,
.feature-column.large .icon-circled{
	width: 7.4rem;
	height: 7.4rem;
	line-height: 7.4rem;
}
.feature-column.left.large .icon-boxed + .feature-text,
.feature-column.left.large .icon-circled + .feature-text{
	padding-left: 10.6rem;
}
.feature-column.right.large .feature-text{
	padding-right: 6rem;
	padding-left: 0;
}
.feature-column.right.large .icon-boxed + .feature-text,
.feature-column.right.large .icon-circled + .feature-text{
	padding-right: 10.6rem;
	padding-left: 0;
}
.feature-column.xlarge .feature-icon{	
	font-size: 5rem;
}
.feature-column.left.xlarge .feature-text{
	padding-left: 7rem;
}
.feature-column.xlarge .icon-boxed,
.feature-column.xlarge .icon-circled{
	width: 8rem;
	height: 8rem;
	line-height: 7.8rem;
}
.feature-column.left.xlarge .icon-boxed + .feature-text,
.feature-column.left.xlarge .icon-circled + .feature-text{
	padding-left: 12.2rem;
}
.feature-column.right.xlarge .feature-text{
	padding-right: 7rem;
	padding-left: 0;
}
.feature-column.right.xlarge .icon-boxed + .feature-text,
.feature-column.right.xlarge .icon-circled + .feature-text{
	padding-right: 12.2rem;
	padding-left: 0;
}
.feature-column.right .feature-icon{
	float: right;
	margin-left: auto;
	margin-right: auto;
}
.feature-column.center .feature-icon{
	display: inline-block;
	float: none;
	margin-left: auto;
	margin-right: auto;
}
.feature-column.center .feature-icon + .feature-text{
	padding-left: 0;
	padding-right: 0;
}
@media only screen and (max-width: 768px) {
	.feature-column.center-on-mobile .feature-icon{
		display: inline-block;
		float: none;
		margin-left: auto;
		margin-right: auto;
	}
	.feature-column.center-on-mobile .feature-icon + .feature-text{
		padding-left: 0;
		padding-right: 0;
	}
}

/*------------------------------------------------------------------
[11. Form Elements]
*/
input,
textarea{
	width: 100%;
	padding: 1.3rem 2.5rem;
	margin: 0 0 1.5rem 0;
}
input, 
select, 
textarea{
	-webkit-appearance: none;
	   -moz-appearance:	none;
			appearance: none;
	-webkit-border-radius: 0;
}
input[type="submit"]{
	width: auto;
}
input:focus,
textarea:focus,
select:focus{
	outline:none;
}
input:disabled,
textarea:disabled,
select:disabled,
.disabled{
	opacity: 0.6;
	cursor: default !important;
}
.disabled *{
	cursor: default !important;
}
.checkbox:disabled, 
.radio:disabled{
	opacity: 0;
	cursor: default;
}
.form-element, 
textarea{
	background: #f9f9f9;
	font-size: 1.3rem;
	color: #666666;
	border: 1px solid #eeeeee;
	border-radius: 0;
}
.form-element:focus,
textarea:focus,
.input-indication .form-element:focus + .inherit-style,
.input-indication.reverse .form-element:focus + .inherit-style{
	background: #fff;
	color: #333;
	border: 1px solid #ccc;
	box-shadow: 0 0 0.2rem 0 rgba(0,0,0,0.2);
	outline: none;
}
textarea{
	min-width: 10rem;
	min-height: 20rem;
}
.textarea-no-resize{
	resize: none;
}
label{
	margin-bottom: 0.8rem;
	color: #666;
	font-size: 1.2rem;
	display: block;
}
label.pull-left{
	margin-bottom: 0;
	margin-right: 1rem;
	padding: 1.4rem 0;
	line-height: normal;
	float: left;
}
label.pull-right{
	margin-bottom: 0;
	margin-left: 1rem;
	padding: 1.4rem 0;
	line-height: normal;
	float: left;
}
label.emphasized{
	color: #666;
	font-size: 1.2rem;
	font-weight: bold;
}

/* Select */
select{
	width: 100%;
	background: transparent;
	border: 0;
	border-radius: 0;
	color: inherit;
	font-size: inherit;
}
.form-select{
	width: 100%;
	padding: 0 !important;
	position: relative;
}
.form-select select{
	width: 100%;
	padding: 1.3rem 2.5rem;
}
.form-select:after{
	font-family: 'icomoon';
	font-size: 1.5rem;
	content: "\e63e";
	pointer-events: none;
	position: absolute;
	top: 1rem;
	right: 2rem;
}

/* Checkbox & Radio */
input[type="checkbox"]{
	-webkit-appearance: checkbox;
	   -moz-appearance:	checkbox;
			appearance: checkbox;
}
.checkbox, 
.radio{
	opacity: 0;
	position: absolute;   
}
.checkbox, 
.checkbox-label, 
.radio, 
.radio-label{
	margin-bottom: 2rem;
	display: inline-block;
	vertical-align: middle;
	cursor: pointer;
}
.checkbox:disabled + .checkbox-label,
.checkbox:disabled + .checkbox-label:before, 
.radio:disabled + .radio-label,
.radio:disabled + .radio-label:before{
	opacity: 0.6;
}
.checkbox-label ~ .checkbox-label,
.radio-label ~ .radio-label{
	margin-left: 1rem;
}
.checkbox-label, 
.radio-label{
	color: #666666;
	position: relative;
}
.checkbox + .checkbox-label:before, 
.radio + .radio-label:before{
	width: 2rem;
	height: 2rem;
	line-height: 2rem;
	margin-right: 1rem;
	display: inline-block;
	vertical-align: middle;
	background: #f9f9f9;
	border: 1px solid #eeeeee;
	content: '';
	font-family: 'icomoon';
	text-align: center;
}
.checkbox:checked + .checkbox-label,
.radio:checked + .radio-label{
	opacity: 1;
}
.checkbox:checked + .checkbox-label:before {
	content: '\e63a';
	background: #ffffff;
	color: #666;
}
.radio:checked + .radio-label:before {
	background: #666;
	box-shadow: inset 0px 0px 0px 4px #ffffff;
}
.checkbox:focus + .checkbox-label, 
.radio:focus + .radio-label {
	outline: none;
}

/* Input Indication */
.input-indication,
.input-indication .input-icon,
.input-indication .form-element{
	margin: 0;
	display: flex;
}
.input-indication{
	margin: 0 0 1.5rem 0;
}
.input-indication .input-icon{
	padding-right: 1.5rem !important;
	padding-left: 1.5rem !important;
	align-items: center;
}
/* reversed and style inherited */
.input-indication.reverse .form-element + .inherit-style,
.input-indication:not(.reverse) .form-element:first-child{
	border-right: none !important
}
.input-indication.reverse .form-element:first-child,
.input-indication:not(.reverse) .form-element + .inherit-style{
	border-left: none !important;
}
.input-indication.reverse .form-element:first-child,
.input-indication.reverse .form-element select{
	padding-left: 0;
}
.input-indication:not(.reverse) .form-element:first-child,
.input-indication:not(.reverse) .form-element select{
	padding-right: 0;
}

/* Style */
.form-element.rounded,
textarea.rounded,
.checkbox.rounded + .checkbox-label:before{
	border-radius: 0.3rem;
}
.radio + .radio-label:before{
	border-radius: 50%;
}
.form-element.pill{
	border-radius: 10.5rem;
}

/* Merged */
@media only screen and (min-width: 768px){
	.merged-form-elements .column:first-child{
		padding-right: 0;
	}
	.merged-form-elements .field-wrapper{
		overflow: visible;
	}
	.merged-form-elements .column:last-child,
	.merged-form-elements .column + .column{
		padding-left: 0;
	}
	.merged-form-elements .column + .column:not(:last-child){
		padding-right: 0;
	}
	.merged-form-elements .column:not(:last-child) .form-element{
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	.merged-form-elements .column + .column .form-element:not(:focus){
		border-left-color: transparent;
	}
	.merged-form-elements .column + .column .form-element{
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}
	.merged-form-elements .column + .column .button{
		width: 100%;
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}
	.merged-form-elements .column .form-element:focus{
		position: relative;
		z-index: 1;
	}
}
.input-indication:not(.reverse) > :first-child,
.input-indication.reverse > :last-child{
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
}
.input-indication:not(.reverse) > :last-child,
.input-indication.reverse > :first-child{
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}
.input-indication.reverse{
	flex-direction: row-reverse;
}

/* Sizes */
.form-element.small{
	padding: 1rem 1.5rem;
	font-size: 1.1rem;
}
.form-element.small select{
	padding: 1rem 3.5rem 1rem 1.5rem;
	font-size: 1.1rem;
}
.form-select.small:after{
	top: 0.5rem;
	right: 1rem;
}
.checkbox.small + .checkbox-label:before, 
.radio.small + .radio-label:before{
	width: 1.6rem;
	height: 1.6rem;
	line-height: 1.6rem;
}
.checkbox.small + .checkbox-label, 
.radio.small + .radio-label{
	font-size: 1.1rem;
}
.form-element.medium{
	padding: 1.3rem 2.5rem;
}
.form-element.medium select{
	padding: 1.3rem 4.5rem 1.3rem 2.5rem;
}
.form-element.large{
	padding: 1.6rem 3rem;
	font-size: 1.4rem;
}
.form-element.large select{
	padding: 1.6rem 5rem 1.6rem 3rem;
	font-size: 1.4rem;
}
.form-select.large:after{
	top: 1.4rem;
	right: 2.5rem;
}
.checkbox.large + .checkbox-label:before, 
.radio.large + .radio-label:before{
	width: 2.4rem;
	height: 2.4rem;
	line-height: 2.4rem;
}
.checkbox.large + .checkbox-label, 
.radio.large + .radio-label{
	font-size: 1.4rem;
}
.form-element.xlarge{
	padding: 1.9rem 3.5rem;
	font-size: 1.5rem;
}
.form-element.xlarge select{
	padding: 1.9rem 5.5rem 1.9rem 3.5rem;
	font-size: 1.5rem;
}
.form-select.xlarge:after{
	top: 1.7rem;
	right: 3rem;
}
.checkbox.xlarge + .checkbox-label:before, 
.radio.xlarge + .radio-label:before{
	width: 2.7rem;
	height: 2.7rem;
	line-height: 2.7rem;
}
.checkbox.xlarge + .checkbox-label, 
.radio.xlarge + .radio-label{
	font-size: 1.5rem;
}

/* Editable Div */
div[contenteditable="true"]{
	background: #f9f9f9;
	color: #666666;
	border: 1px solid #eeeeee;
}
div[contenteditable="true"]:focus{
	background: #ffffff;
	color: #333333;
	border: 1px solid #cccccc;
	box-shadow: 0 0 0.2rem 0 rgba(0,0,0,0.2);
	outline: none;
}

/* Sizes */
div[contenteditable="true"]{
	padding: 1.3rem 2.5rem;
}
div[contenteditable="true"].small{
	padding: 1rem 1.5rem;
	font-size: 1rem;
}
div[contenteditable="true"].medium{
	padding: 1.3rem 2.5rem;
}
div[contenteditable="true"].large{
	padding: 1.6rem 3rem;
	font-size: 1.4rem;
}
div[contenteditable="true"].xlarge{
	padding: 1.9rem 3.5rem;
	font-size: 1.5rem;
}

/*------------------------------------------------------------------
[12. Buttons]
*/
.button-group{
	margin-bottom: 3rem;
}
button, 
.button{
	padding: 1.3rem 2.5rem;
	margin: 0 1rem 1rem 0;
	font-family: inherit;
    font-size: 1.3rem;
    color: #666666;
	background-color: #eeeeee;
	border: 1px solid #eeeeee;
    font-weight: normal;
    vertical-align: middle;
	display: inline-block;
	cursor: pointer;
	-webkit-appearance: none;
	   -moz-appearance:	none;
			appearance:	none;
}
.left-on-mobile button,
.left-on-mobile .button{
	margin-right: 1rem;
	margin-left: 0;
}
.center button,
.center .button,
.center-on-mobile button,
.center-on-mobile .button{
	margin-right: 0.5rem;
	margin-left: 0.5rem;
}
.right button + button,
.right .button + .button,
.right-on-mobile button + button,
.right-on-mobile .button + .button{
	margin-right: 0;
	margin-left: 1rem;
}
form button, 
form .button{
	margin-right: 0 !important;
	margin-left: 0 !important;
}
button:focus, 
.button:focus{
	outline: none;
}
button:hover, 
.button:hover{
	text-decoration: none;
	color: #666666;
	background-color: #d0d0d0;
	border-color: #d0d0d0;
}
/* Sizes */
button.small, 
.button.small{
	padding: 1rem 1.5rem;
	font-size: 1.1rem !important;
}
button.medium, 
.button.medium{
	padding: 1.3rem 2.5rem;
	font-size: 1.3rem !important;
}
button.large, 
.button.large{
	padding: 1.6rem 3rem;
	font-size: 1.4rem !important;
}
button.xlarge, 
.button.xlarge{
	padding: 1.9rem 3.5rem;
	font-size: 1.5rem !important;
}

/* Icon Button */
button [class*="icon-"],
.button [class*="icon-"]{
	line-height: inherit;
}
button [class*="icon-"].left,
.button [class*="icon-"].left{
	margin-right: 0.5rem;
}
button [class*="icon-"].right,
.button [class*="icon-"].right{
	margin-left: 0.5rem;
}

/* Button Content */
.button-content > span{
	display: inline-block;
}
.button-content small{
	display: block;
}

/* Firefox Fix */
input[type="submit"]::-moz-focus-inner{
	border: 0 !important;
}

/* Styles */
button.rounded, 
.button.rounded{
	border-radius: 0.3rem;
}
button.pill, 
.button.pill{
	border-radius: 10.5rem;
}
button.shadow, 
.button.shadow{
	-webkit-box-shadow: 0 0.4rem 0.8rem 0 rgba(0,0,0,.3);
	   -moz-box-shadow: 0 0.4rem 0.8rem 0 rgba(0,0,0,.3);
		    box-shadow: 0 0.4rem 0.8rem 0 rgba(0,0,0,.3);
}
button.hard-shadow,
.button.hard-shadow{
	border-bottom: 3px solid rgba(0,0,0,.15);
}
button.disabled, 
.button.disabled{
	cursor: default;
	color: #cccccc;
	background-color: #f9f9f9;
	border-color: #eeeeee; 
}
button.disabled:hover, 
.button.disabled:hover{
	background-color: #f9f9f9;
	border-color: #eeeeee;
}

/* Button Nav */
.button-nav{
	padding: 0;
	margin: 0;
	list-style: none;
}
.button-nav li{
	margin: 0 1px 0 0;
	float: left;
}
.button-nav li .button.rounded,
.button-nav li button.rounded,
.button-nav li .button.pill,
.button-nav li button.pill{
	border-radius: 0;
}
.button-nav li:first-child .button.rounded,
.button-nav li:first-child button.rounded,
.button-nav.rounded li:first-child .button,
.button-nav.rounded li:first-child button{
	border-radius: 0.3rem 0 0 0.3rem;
}
.button-nav li:last-child .button.rounded
.button-nav li:last-child button.rounded,
.button-nav.rounded li:last-child .button,
.button-nav.rounded li:last-child button{
	border-radius: 0 0.3rem 0.3rem 0;
}
.button-nav li:first-child .button.pill,
.button-nav li:first-child button.pill,
.button-nav.pill li:first-child .button,
.button-nav.pill li:first-child button{
	border-radius: 10.5rem 0 0 10.5rem;
}
.button-nav li:last-child .button.pill,
.button-nav li:last-child button.pill,
.button-nav.pill li:last-child .button,
.button-nav.pill li:last-child button{
	border-radius: 0 10.5rem 10.5rem 0;
}
.button-nav.center li{
	margin-left: -0.3rem;
	display: inline-block;
	float: none;
}

/*------------------------------------------------------------------
[13. Dropdowns]
*/
.dropdown{
	position: relative;
	margin-bottom: 1rem;
}
.dropdown button, 
.dropdown .button{
	margin: 0;
	text-align: left;
}
.dropdown.disabled .button{
	cursor: default;
}
.dropdown.disabled button, 
.dropdown.disabled .button,
.dropdown.disabled button:hover, 
.dropdown.disabled .button:hover{
	border-color: #ddd !important;
	background: #fff !important;
	color: #ddd !important;
}
.dropdown-list{
	width: 16rem;
	margin: 0.3rem 0 0 0;
	text-align: left;
	background: #fff;
	border: 1px solid #ddd;
	box-shadow: 0 0 0.2rem rgba(0,0,0,.3);
	border-radius: 0.3rem;
	opacity: 0;
	visibility: hidden;
	list-style: none;
	position: absolute;
	z-index: 101;
	-webkit-transition-property: -webkit-transform, opacity, visibility;
			transition-property: transform, opacity, visibility;
	-webkit-transition-duration: 400ms;
			transition-duration: 400ms;
}
.list-up .dropdown-list{
	box-shadow: 0 0 0.2rem rgba(0,0,0,.3);
}
.right .dropdown-list{
	right: 0;
}
.dropdown-list:not(.custom-content) li a{
	padding: 0.6rem 2.5rem;
	display: block;
	font-size: 1.2rem;
	color: #666;
	font-weight: normal;
	cursor: pointer;
}
.dropdown-list li a:hover{
	background: #f4f4f4;
	color: #333;
	text-decoration: none;
}
.dropdown-list li.current a{
	background: #f4f4f4;
	color: #666;
}
.dropdown-list.active{
	opacity: 1;
	visibility: visible;
}
.dropdown-list .list-header{
	padding: 0.6rem 2.5rem;
	font-size: 1.1rem;
	font-weight: bold;
	background: #ddd;
}
.dropdown.small .button,
.dropdown.small button{
	padding: 1rem 1.5rem;
}
.dropdown.small .dropdown-list li a{
	padding: 0.4rem 1.5rem;
}
.dropdown.small .dropdown-list{
	width: 14rem;
}
.dropdown.medium .button,
.dropdown.medium button{
	padding: 1.3rem 2.5rem;
} 
.dropdown.medium .dropdown-list li a{
	padding: 0.6rem 2.5rem;
}
.dropdown.medium .dropdown-list{
	width: 16rem;
}
.dropdown.large .button,
.dropdown.large button{
	padding: 1.6rem 3rem;
} 
.dropdown.large .dropdown-list li a{
	padding: 0.9rem 3rem;
}
.dropdown.large .dropdown-list{
	width: 18rem;
}
.dropdown.xlarge .button,
.dropdown.xlarge button{
	padding: 1.9rem 3.5rem;
} 
.dropdown.xlarge .dropdown-list li a{
	padding: 1.2rem 3.5rem;
}
.dropdown.xlarge .dropdown-list{
	width: 20rem;
}
.dropdown.rounded .button,
.dropdown.rounded button{
	border-radius: 0.3rem;
}

/*------------------------------------------------------------------
[14. Lists]
*/
ul, ol {
	margin: 0 0 3rem 2rem;
	padding: 0;
	font-size: 1.4rem;
}
dl{
	margin: 0 0 3rem 0;
}
dt {
	font-weight: bold;
}
dd{
	padding-left: 3rem;
	margin: 0 0 1rem 0;
}
ul {
	list-style: disc outside;
}
ol {
	list-style: decimal outside;
}
ul ul, ul ol,
ol ol, ol ul {
	margin: 0.4rem 0 0.4rem 3rem;
}
.list-unstyled,
.list-inline {
	margin-left: 0;
	list-style: none;
}
.list-inline li{
	display: inline-block;
}
.list-inline.right li{
	margin-left: 1rem;
}
.list-inline.center li{
	margin-left: 0.5rem;
	margin-right: 0.5rem;
}
.list-inline.left li{
	margin-right: 1rem;
}

/* Icon List */
ul li > [class*="icon-"]:first-child,
ol li > [class*="icon-"]:first-child{
	margin-right: 1rem;
}

/* List Group */
.list-group.solid, 
.list-group.dashed,
.list-group-dotted{
	border: none;
}
.list-group{
	margin-left: 0;
	list-style: none;
}
.list-group li{
	padding: 1rem 0;
	border-bottom: 1px solid #eeeeee;
}
.list-group li li{
	border-top: 1px solid #eeeeee;
}
.list-group > li:first-child{
	padding-top: 0;
}
.list-group li li:last-child{
	padding-bottom: 0;
}
.list-group li:last-child{
	border-bottom: none;
	border-width: 0;
}
.list-group ul,
.list-group ol{
	margin: 0; 
}
.list-group.boxed li{
	padding: 1rem 2rem;
}
.list-group.boxed li li:last-child{
	padding: 1rem 2rem;
	border-top: 1px solid;
}
.list-group li ul{
	padding-top: 1rem;
}
.list-group[class*="border-"] li,
.list-group[class*="border-"] li li,
.list-group[class*="border-"] li ul{
	border-color: inherit !important;
}
.list-group li *:last-child{
	margin-bottom: 0;
}

/* Sizes */
.list-group.small li li{
	padding-left: 1.5rem;
	margin-top: 0;
}
.list-group.small li{
	padding: 0.7rem 0;
}
.list-groups.small ul,
.list-group.small ol{
	margin-top: 0.7rem !important;
}
.list-group.boxed.small li, 
.list-groups.boxed.small li{
	padding: 0.7rem 1.5rem;
}
.list-group.medium li{
	padding: 1rem 0;
}
.list-groups.medium ul,
.list-group.medium ol{
	margin-top: 1rem !important;
}
.list-group.boxed.medium li,
.list-group.boxed.medium li{
	padding: 1rem 2rem;
}
.list-group.large li{
	padding: 1.3rem 0;
}
.list-groups.large ul,
.list-group.large ol{
	margin-top: 1.3rem !important;
}
.list-group.boxed.large li, 
.list-group.boxed.large li{
	padding: 1.3rem 2.5rem;
}
.list-group.xlarge li{
	padding: 1.6rem 0;
}
.list-groups.xlarge ul,
.list-group.xlarge ol{
	margin-top: 1.6rem !important;
}
.list-group.boxed.xlarge li,
.list-group.boxed.xlarge li{
	padding: 1.6rem 3rem;
}

/* Styles */
.list-group.dotted li{
	border-bottom-style: dotted;
}
.list-group.dotted li li{
	border-top-style: dotted;
}
.list-group.dashed li{
	border-bottom-style: dashed;
}
.list-group.dashed li li{
	border-top-style: dashed;
}
.list-group.thick li{
	border-bottom-width: 0.2rem;
}
.list-group.thick li li{
	border-top-width: 0.2rem;
}
.list-group:not(.boxed) li:last-child{
	border-bottom: none;
	padding-bottom: 0 !important;
}
.list-group li li:last-child{
	border-top: none;
}
.list-group.boxed li{
	border: 1px solid;
	border-bottom: none;
}
.list-group.boxed li:last-child{
	border-bottom: 1px solid;
}
.list-group.boxed.dotted li{
	border-style: dotted;
	border-bottom: none;
}
.list-group.boxed.dotted li:last-child{
	border-bottom: 1px dotted;
}
.list-group.boxed.dotted li li:last-child{
	border-top-style: dotted;
}
.list-group.boxed.dashed li{
	border-style: dashed;
	border-bottom: none;
}
.list-group.boxed.dashed li:last-child{
	border-bottom: 1px dashed;
}
.list-group.boxed.dashed li li:last-child{
	border-top-style: dashed;
}
.list-group.boxed.thick li, 
.list-group.boxed.thick li:last-child{
	border-width: 0.2rem;
}
.list-group.rounded li:first-child{
	border-radius: 0.3rem 0.3rem 0 0;
}
.list-group.rounded li:last-child{
	border-radius: 0 0 0.3rem 0.3rem;
}
.list-group[class*="border-"],
.list-group[class*="border-"].dotted, 
.list-group[class*="border-"].dashed, 
.list-group[class*="border-"].thick,
.list-group[class*="border-"]:hover,
.list-group.boxed[class*="border-"], 
.list-group.boxed[class*="border-"].dotted, 
.list-group.boxed[class*="border-"].dashed, 
.list-group.boxed[class*="border-"].thick,
.list-group.boxed[class*="border-"]:hover {
	border-width: 0;
}
.list-horizontal li{
	display: inline-block;
	list-style: none;
}

/*------------------------------------------------------------------
[15. Menu Lists]
*/
.menu-box{
	overflow: visible;
	margin-bottom: 3rem;
}
.menu-list,
.menu-list li{
	margin: 0 0 3rem 0;
	list-style: none;
}
.menu-list.boxed{
	padding: 2rem;
}
.menu-list li{
	position: relative;
}
.menu-list li:last-child{
	margin-bottom: 0;
}
.menu-list h4{
	margin-bottom: 0;
}
.menu-list h4 + p{
	margin-top: 1.3rem;
}
.menu-list p{
	margin-bottom: 0;
}
.menu-list .menu-content{
	padding-right: 10rem;
}
.menu-list .menu-title,
.menu-list .menu-description,
.menu-list .menu-price{
	background-color: #fff;
}
.menu-list h4,
.menu-list .menu-title,
.menu-list .menu-content,
.menu-list .menu-description,
.menu-list .menu-price{
	position: relative;
	z-index: 1;
}
.menu-list .menu-price{
	font-weight: bold;
	position: absolute;
	right: 0;
	bottom: 0;
}
.menu-title .menu-price{
	position: relative;
	right: auto;
	left: auto;
	bottom: auto;
}
.menu-list .menu-line{
	height: 1px;
	margin: 0;
	border: 0;
	display: block;
	position: absolute;
	bottom: 8px;
	left: 0;
	right: 0;
	z-index: 0;
	background-image: linear-gradient(to right, #ccc 100%, rgba(0,0,0,0) 0%);
	background-position: bottom;
	background-size: 6px 2px;
	background-repeat: repeat-x;
}
.menu-list.menu-items-inline h4, 
.menu-list.menu-items-inline .menu-content{
	display: inline;
}

/* Callout */
.menu-list .callout{
	padding: 2rem;
	background-color: #f4f4f4;
}
.menu-list .callout *{
	background-color: inherit;
}
.menu-list .special-item{
	font-size: 1.3rem;
	font-style: italic;
	text-transform: none;
}

/* Line bottom position */
.menu-list h4 .menu-line{
	bottom: 4px;
}
.menu-list .menu-content .menu-line{
	bottom: 8px;
}

/* Sizes */
.menu-box.small,
.menu-list.boxed.small{
	padding: 1.5rem 1.5rem 0 1.5rem;
}
.menu-box.small .menu-list,
.menu-list.boxed.small li:last-child{
	margin-bottom: 1.5rem
}
.menu-box.medium,
.menu-list.boxed.medium{
	padding: 2rem 2rem 0 2rem;
}
.menu-box.medium .menu-list,
.menu-list.boxed.medium li:last-child{
	margin-bottom: 2rem
}
.menu-box.large,
.menu-list.boxed.large{
	padding: 2.5rem 2.5rem 0 2.5rem;
}
.menu-box.large .menu-list,
.menu-list.boxed.large li:last-child{
	margin-bottom: 2.5rem
}
.menu-box.xlarge,
.menu-list.boxed.xlarge{
	padding: 3rem 3rem 0 3rem;
}
.menu-box.xlarge .menu-list,
.menu-list.boxed.xlarge li:last-child{
	margin-bottom: 3rem
}

/* Alignment */
.menu-list.center .menu-line{
	display: none;
}
.menu-list.center .menu-price{
	position: relative;
}
.menu-list.center .menu-content{
	padding: 0;
}
.menu-list.center .menu-description{
	display: block;
}
.menu-list.right .menu-content{
	padding-left: 10rem;
	padding-right: 0;
}
.menu-list.right .menu-price{
	left: 0;
	right: auto;
}

/* Styles */
.menu-list.boxed{
	border-style: solid;
	border-width: 1px;
}
.menu-list.boxed.dashed{
	border-style: dashed;
}
.menu-list.boxed.dotted{
	border-style: dotted;
}
.menu-list.rounded,
.menu-list.rounded .callout{
	border-radius: 0.3rem;
}
.dotted .menu-line{
	height: 2px;
	background-image: radial-gradient(circle closest-side, #ccc 99%,rgba(0,0,0,0) 0%);
}
.dashed .menu-line{
	height: 1px;
	background-image:  linear-gradient(to right, #ccc 50%, rgba(0,0,0,0) 0%);
}
.menu-list .callout.rounded{
	border-radius: 0.3rem;
}

/*------------------------------------------------------------------
[16. Timeline]
*/

.timeline{
	margin: 0 0 3rem 0;
	list-style: none;
}
.timeline li{
	padding: 0;
	margin: 0;
}
.timeline li:last-child{
	margin-bottom: 0;
}
.timeline .timeline-title,
.timeline .timeline-description{
	position: relative;
}
.timeline li:last-child .timeline-description *:last-child{
	margin-bottom: 0;
}
.timeline.vertical-indication .timeline-title{
	padding-right: 5rem;
	z-index: 1;
}
.timeline.vertical-indication .timeline-description{
	padding-left: 5rem;
}
.timeline.vertical-indication .timeline-title:after{
	width: 1.1rem;
	height: 1.1rem;
	border-radius: 50%;
	content: "";
	border: 2px solid #333;
	background-color: #fff;
	position: absolute;
	left: 100%;
	top: 0;
	z-index: 100;
}
.timeline.vertical-indication .timeline-description:before{
	width: 0.1rem;
	content: "";
	background-color: #eee;
	position: absolute;
	left: 0.5rem;
	top: 0;
	bottom: 0;
}
.timeline.vertical-indication.thick .timeline-description:before{
	width: 0.3rem;
	left: 0.4rem;
}
.timeline.vertical-indication li:last-child .timeline-description:before{
	display: none;
}

/*------------------------------------------------------------------
[17. Tabs]
*/
.tabs{
	margin: 0 0 3rem 0;
	padding: 0;
}
.tab-nav{
	margin: 0;
	padding: 0;
	font-size: 0;
	list-style: none;
}
.tab-nav:after{
	height:0;
	display: block;
	content: ".";
	overflow: hidden;
	clear: both;
}
.tabs .tab-nav > li{
	margin: 0 1px 0 0;
	display: inline-block;
}
.tabs.vertical:after{
	display: block;
	content: ".";
	overflow: hidden;
	clear: both;
}
.tabs.vertical .tab-nav > li{
	margin: 0 0 1px 0;
	display: block;
	float: none;
}
.tabs.vertical .tab-nav{
	width: 25%;
	float: left;
}
.tabs .tab-nav > li:last-child{
	margin-right: 0;
}
.tabs.right .tab-nav > li{
	margin: 0 0 0 1px;
}
.tabs.right.vertical .tab-nav > li{
	margin: 0 0 1px 0;
	float: none;
}
.tabs.right.vertical .tab-nav{
	width: 25%;
	float: right;
}
.tabs .tab-nav > li.active a:hover,
.tabs .tab-nav > li.active a{
	background: #ffffff;
	border-top: 1px solid #eeeeee;
	border-right: 1px solid #eeeeee;
	border-bottom: 1px solid transparent;
	border-left: 1px solid #eeeeee;
	cursor: default;
}
.tabs.vertical .tab-nav > li.active a:hover,
.tabs.vertical .tab-nav > li.active a{
	background: #ffffff;
	border-top: 1px solid #eeeeee;
	border-right: 1px solid transparent;
	border-bottom: 1px solid #eeeeee;
	border-left: 1px solid #eeeeee;
	cursor: default;
	position: relative;
}
.tabs.right.vertical .tab-nav > li.active a:hover,
.tabs.right.vertical .tab-nav > li.active a{
	background: #ffffff;
	border-top: 1px solid #eeeeee;
	border-right: 1px solid #eeeeee;
	border-bottom: 1px solid #eeeeee;
	border-left: 1px solid transparent;
}
.tabs .tab-nav > li a,
.tabs .tab-nav > li.disabled a:hover{
	padding: 1.3rem 2.5rem;
	margin: 0;
	font-size: 1.3rem;
	font-weight: normal;
	line-height: 1.4;
	text-decoration: none;
	vertical-align: middle;
	display: inline-block;
	color: #666666;
	background-color: #eeeeee;
	border: 1px solid #eeeeee;
	cursor: pointer;
}
.tabs.vertical .tab-nav > li a{
	display: block;
}
.tabs .tab-nav > li a:hover{
	text-decoration: none;
	color: #666666;
	background: #d0d0d0;
	border-color: #d0d0d0;
}
.tab-panes{
	margin-top: -1px;
	padding: 2.5rem;
	background: #fff;
	border: 1px solid #eeeeee;
}
.tabs.vertical .tab-panes{
	width: 75%;
	margin-top:0;
	margin-left: -1px;
	float: left;
}
.tabs.right.vertical .tab-panes{
	margin-left:0;
	margin-right: -1px;
	float: right;
}
.tab-panes > div{
	display: none;
	background: none;
}
.tab-panes .tab-content{
	opacity: 0;
	-webkit-transition-property: opacity, visibility;
			transition-property: opacity, visibility;
	-webkit-transition-duration: 400ms;
			transition-duration: 400ms;
}
.tab-panes .animate-in .tab-content{
	opacity: 1;
}
.tab-panes > div.active{
	display: block;
	content: ".";
	overflow: hidden;
	clear: both;
}
.tab-panes > div > *:first-child{
	margin-top: 0;
}
.tab-panes > div *:not(.form-element):not(.input-indication):not(.radio-label):not(.checkbox-label):last-child{
	margin-bottom: 0;
}

/* Sizes */
.tabs.small .tab-nav > li a{
	padding: 1rem 1.5rem;
	font-size: 1.1rem;
}
.tabs.small .tab-panes{
	padding: 1.5rem;
}
.tabs.medium .tab-nav > li a{
	padding: 1.3rem 2.5rem;
	font-size: 1.3rem;
}
.tabs.medium .tab-panes{
	padding: 2.5rem;
}
.tabs.large .tab-nav > li a{
	padding: 1.6rem 3rem;
	font-size: 1.4rem;
}
.tabs.large .tab-panes{
	padding: 3rem;
}
.tabs.xlarge .tab-nav > li a{
	padding: 1.9rem 3.5rem;
	font-size: 1.5rem;
}
.tabs.xlarge .tab-panes{
	padding: 3.5rem;
}

/* Styles */
.tabs.rounded .tab-nav > li:first-child a{
	border-radius: 0.3rem 0 0 0;
}
.tabs.right.rounded .tab-nav > li:first-child a{
	border-radius: 0 0.3rem 0 0;
}
.tabs.rounded .tab-nav > li:last-child a{
	border-radius: 0 0.3rem 0 0;
}
.tabs.right.rounded .tab-nav > li:last-child a{
	border-radius: 0.3rem 0 0 0;
}
.tabs.vertical.rounded .tab-nav > li:last-child a{
	border-radius: 0 0 0 0.3rem;
}
.tabs.right.vertical.rounded .tab-nav > li:last-child a{
	border-radius: 0 0 0.3rem 0;
}
.tabs.rounded .tab-panes{
	border-radius: 0 0.3rem 0.3rem 0.3rem;
}
.tabs.right.rounded .tab-panes{
	border-radius: 0.3rem 0 0.3rem 0.3rem;
}
.tabs.solid .tab-nav > li.active a:hover,
.tabs.solid .tab-nav > li.active a,
.tabs.solid .tab-panes{
	border-style: solid;
}
.tabs.dotted .tab-nav > li.active a:hover,
.tabs.dotted .tab-nav > li.active a,
.tabs.dotted .tab-panes{
	border-style: dashed;
}
.tabs.dashed .tab-nav > li.active a:hover,
.tabs.dashed .tab-nav > li.active a,
.tabs.dashed .tab-panes{
	border-style: dashed;
}
.tabs.thick .tab-nav > li.active a:hover,
.tabs.thick .tab-nav > li.active a,
.tabs.thick .tab-panes{
	border-width: 0.2rem;
}
.tabs[class*="border-"]{
	border: none;
}

/*------------------------------------------------------------------
[18. Accordion]
*/
.accordion{
	margin: 0 0 3rem 0;
	padding: 0;
	border: none !important;
}
.accordion > ul{
	margin: 0;
	padding: 0;
	list-style: none;
}
.accordion:after{
	height:0;
	display: block;
	content: ".";
	overflow: hidden;
	clear: both;
}
.accordion > ul > li{
	margin: 0 0 1px 0;
	display: block;
}
.accordion > ul > li.active > a{
	background: none;
	border-top: 1px solid #eeeeee;
	border-right: 1px solid #eeeeee;
	border-bottom: 1px solid transparent;
	border-left: 1px solid #eeeeee;
}
.accordion > ul > li > a{
	padding: 1.3rem 2.5rem;
	margin: 0;
    font-size: 1.3rem;
    font-weight: normal;
    line-height: 1.4;
    text-decoration: none;
    vertical-align: middle;
	display: block;
	color: #666666;
	background-color: #eeeeee;
	border: 1px solid #eeeeee;
	cursor: pointer;
}
.accordion > ul > li > a:hover{
	text-decoration: none;
	color:#666666;
	background: #d0d0d0;
	border-color:#d0d0d0;
}
.accordion > ul > li > div{
	height: 0;
	visibility: hidden;
	border: 1px solid #eeeeee;
	overflow: hidden;
	-webkit-transition-property: height, visibility;
			transition-property:  height, visibility;
	-webkit-transition-duration: 300ms;
			transition-duration: 300ms;
}
.accordion > ul > li.active > div{
	height: auto;
	visibility: visible;
}
.accordion .accordion-content{
	height: auto;
	padding: 2.5rem;
	overflow: hidden;
	display: block;
}
.accordion > ul > li > div > *:first-child{
	margin-top: 0;
}
.accordion > ul > li > div *:not(.form-element):not(.input-indication):not(.radio-label):not(.checkbox-label):last-child{
	margin-bottom: 0;
}

/* Sizes */
.accordion.small li > a{
	padding: 1rem 1.5rem;
	font-size: 1.1rem;
}
.accordion.small .accordion-content{
	padding: 1.5rem;
}
.accordion.medium li > a{
	padding: 1.3rem 2.5rem;
	font-size: 1.3rem;
}
.accordion.medium .accordion-content{
	padding: 2.5rem;
}
.accordion.large li > a{
	padding: 1.6rem 3rem;
	font-size: 1.4rem;
}
.accordion.large .accordion-content{
	padding: 3rem;
}
.accordion.xlarge li > a{
	padding: 1.9rem 3.5rem;
	font-size: 1.5rem;
}
.accordion.xlarge .accordion-content{
	padding: 3.5rem;
}

/* Styles */
.accordion.rounded li:first-child > a{
	border-radius: 0.3rem 0.3rem 0 0;
}
.accordion.rounded li:last-child > a{
	border-radius: 0 0 0.3rem 0.3rem;
}
.accordion.rounded li > div{
	border-radius: 0 0 0.3rem 0.3rem;
}
.accordion.rounded li.active:last-child > a:hover{
	border-radius: 0;
}
.accordion.solid li > a,
.accordion.solid li > div{
	border-style: solid;
}
.accordion.dotted li > a,
.accordion.dotted li > div{
	border-style: dashed;
}
.accordion.dashed li > a,
.accordion.dashed li > div{
	border-style: dashed;
}
.accordion.thick li > a,
.accordion.thick li > div{
	border-width: 0.2rem;
}

/*------------------------------------------------------------------
[19. Tables]
*/
.table{
	width: 100%;
	margin-bottom: 3rem;
	border-width: 1px 1px 0 1px;
	border-color: #eeeeee;
	border-style: solid;
	font-size: 1.2rem;
	line-height: 1.5;
	text-align: left;
	border-collapse: separate;
	border-spacing: 0;
}
.table > thead > tr > th, 
.table > tbody > tr > th, 
.table > tfoot > tr > th, 
.table > thead > tr > td, 
.table > tbody > tr > td, 
.table > tfoot > tr > td{
	padding: 1rem 1.5rem;
}
.table > thead{
	color:#666666;
	background-color: #eeeeee;
}
.table th + th,
.table td{
	border-left: 1px solid #eeeeee;
	border-bottom: 1px solid #eeeeee;
}
.table > thead > tr > th{
	border-bottom: none;
}

/* Sizes */
.table.small > thead > tr > th,
.table.small > tbody > tr > th,
.table.small > tfoot > tr > th,
.table.small > thead > tr > td,
.table.small > tbody > tr > td,
.table.small > tfoot > tr > td{
	padding: 0.7rem 1rem;
	font-size: 1rem;
}
.table.medium > thead > tr > th,
.table.medium > tbody > tr > th,
.table.medium > tfoot > tr > th,
.table.medium > thead > tr > td,
.table.medium > tbody > tr > td,
.table.medium > tfoot > tr > td{
	padding: 1rem 1.5rem;
}
.table.large > thead > tr > th,
.table.large > tbody > tr > th,
.table.large > tfoot > tr > th,
.table.large > thead > tr > td,
.table.large > tbody > tr > td,
.table.large > tfoot > tr > td{
	padding: 1.3rem 2.5rem;
	font-size: 1.4rem;
}
.table.xlarge > thead > tr > th,
.table.xlarge > tbody > tr > th,
.table.xlarge > tfoot > tr > th,
.table.xlarge > thead > tr > td,
.table.xlarge > tbody > tr > td,
.table.xlarge > tfoot > tr > td{
	padding: 1.6rem 3rem;
	font-size: 1.5rem;
}

/* Styles */
.table.rounded{
	border-radius: 0.3rem;
}
.table.striped tbody tr:nth-child(even) td, 
.table.striped tbody tr:nth-child(even) th{
	background-color: #eeeeee;
}

/*------------------------------------------------------------------
[20. Pricing Tables]
*/
.pricing-table{
	width: 100%;
	margin-bottom: 3rem;
	text-align: center;
}
.pricing-table.left{
	text-align: left;
}
.pricing-table.right{
	text-align: right;
}
.pricing-table:before, 
.pricing-table:after{
	content: ".";
	display: block;
	height: 0;
	overflow-x: hidden;
	overflow-y: hidden;
}
.pricing-table:after{
	clear: both;
}

/* Columns */
.pricing-table.columns-1 .pricing-table-column{
	width: 100%;
}
.pricing-table.columns-2 .pricing-table-column{
	width: 50%;
}
.pricing-table.columns-3 .pricing-table-column{
	width: 33.33333%;
}
.pricing-table.columns-4 .pricing-table-column{
	width: 25%;
}
.pricing-table.columns-5 .pricing-table-column{
	width: 20%;
}
.pricing-table-column{
	float: left;
}
[class*="border-"] .pricing-table-column{
	border-width: 1px 1px 1px 0;
	border-style: solid;
}
.pricing-table-column:first-child{
	border-width: 1px;
}
.pricing-table-column > *:not(hr){
	padding: 3rem;
}
.pricing-table-column > *:not(:first-child){
	padding-top: 0;
}
.pricing-table-column > [class*="bkg-"]{
	padding: 3rem;
}
.pricing-table-column > hr{
	margin: 3rem;
}
.pricing-table-column > hr{
	margin-top: 0;
}
.pricing-table-column.callout{
	margin-left: -1px;
	margin-bottom: -2rem;
	border: 1px solid #ffffff;
	box-shadow: 0rem 0rem 0.8rem rgba(0,0,0,.2);
	position: relative;
	top:-1.5rem;
	z-index: 1;
}
.pricing-table-column.callout > .pricing-table-header{
	padding-top: 4.5rem;
}
.pricing-table-column.callout > .pricing-table-footer{
	padding-bottom: 4.5rem;
}
.pricing-table-header h2{
	margin:0;
	font-size: 2.5rem;
}
.pricing-table-header[class*="bkg-"] h2{
	color: #ffffff;
}
.pricing-table-price{
	color: #666666;
}
.pricing-table-price h4{
	margin:0;
	font-size: 3rem;
}
.pricing-table-price[class*="bkg-"] h4{
	color:#ffffff;
}
.pricing-table-price .currency{
	margin-right: 0.2rem;
	font-size: 1.3rem;
	position: relative;
	top: -1rem;
}
.pricing-table-price .interval{
	font-size: 1.3rem;
	opacity: 0.5;
}
.pricing-table-options,
.pricing-table-options ul{
	margin: 0;
	list-style: none;
	line-height: inherit;
}
.pricing-table-options del{
	opacity: 0.5;
	text-decoration: none;
}
.pricing-table-options li{
	margin-bottom: 0;
	padding: 0.7rem 0;
}
.pricing-table-options.with-icon li:before{
	margin-right: 1rem;
	font-family: icomoon;
	content: "\e63a";
	float: left;
}
.pricing-table-text{
	line-height: inherit;
}
.pricing-table-text p:last-child{
	margin-bottom: 0;
}
.pricing-table-footer button,
.pricing-table-footer .button{
	margin-bottom: 0;
}

/* Border Color */
.pricing-table[class*="border-"]{
	border-width: 0;
}
.pricing-table[class*="border-"] .pricing-table-column{
	border-color: inherit !important;
}

/* Sizes */
.pricing-table.small .pricing-table-header h2{
	font-size: 2.5rem;
}
.pricing-table.small .pricing-table-price .currency{
	font-size: 1.3rem;
	top: -1rem;
}
.pricing-table.small .pricing-table-price .interval{
	font-size: 1.1rem;
}
.pricing-table.small .pricing-table-price h4{
	font-size: 3rem;
}

.pricing-table.medium .pricing-table-header h2{
	font-size: 3rem;
}
.pricing-table.medium .pricing-table-price .currency{
	font-size: 1.6rem;
	top: -1.4rem;
}
.pricing-table.medium .pricing-table-price .interval{
	font-size: 1.3rem;
}
.pricing-table.medium .pricing-table-price h4{
	font-size: 3.6rem;
}
.pricing-table.large .pricing-table-header h2{
	font-size: 3rem;
}
.pricing-table.large .pricing-table-price .currency{
	font-size: 1.6rem;
	top: -2rem;
}
.pricing-table.large .pricing-table-price .interval{
	font-size: 1.4rem;
}
.pricing-table.large .pricing-table-price h4{
	font-size: 4.5rem;
}
.pricing-table.xlarge .pricing-table-header h2{
	font-size: 3.2rem;
}
.pricing-table.xlarge .pricing-table-price .currency{
	font-size: 2rem;
	top: -2.7rem;
}
.pricing-table.xlarge .pricing-table-price .interval{
	font-size: 1.6rem;
}
.pricing-table.xlarge .pricing-table-price h4{
	font-size: 6rem;
}

/* Styles */
.pricing-table.rounded .pricing-table-column:first-child{
	border-radius: 0.3rem 0 0 0.3rem;
}
.pricing-table.rounded .pricing-table-column:first-child > div:first-child{
	border-top-left-radius: inherit;
}
.pricing-table.rounded .pricing-table-column:first-child > div:left-child{
	border-bottom-left-radius: inherit;
}
.pricing-table.rounded .pricing-table-column:last-child{
	border-radius: 0 0.3rem 0.3rem 0;
}
.pricing-table.rounded .pricing-table-column:last-child > div:first-child{
	border-top-right-radius: inherit;
}
.pricing-table.rounded .pricing-table-column:last-child > div:last-child{
	border-bottom-right-radius: inherit;
}
.pricing-table-column.rounded,
.pricing-table.rounded.columns-1 .pricing-table-column,
.pricing-table.rounded .pricing-table-column.callout{
	border-radius: 0.3rem;
}
.pricing-table.rounded .pricing-table-column.callout > div:first-child{
	border-top-left-radius: inherit;
	border-top-right-radius: inherit;
}
.pricing-table.rounded .pricing-table-column.callout > div:lst-child{
	border-bottom-left-radius: inherit;
	border-bottom-left-radius: inherit;
}

/*------------------------------------------------------------------
[21. Boxes]
*/
.box{
	width: 100%;
	padding: 2.5rem;
	margin-bottom: 3rem;
	border: 1px solid #eeeeee;
	overflow: hidden;
	position: relative;
}
.box .box{
	width: auto;
	padding: inherit;
}
.grid-item .box,
.equalize .column > .box{
	height: 100%;
}
.box.dotted{
	border-style: dotted;
}
.box.dashed{
	border-style: dashed;
}
.box > *:first-child{
	margin-top: 0;
}
.box *:not(.form-element):not(.form-submit):last-child{
	margin-bottom: 0;
}
.box > a:not(.button){
	color: inherit;
	font-weight: bold;
}
.box > a:not(.button):hover{
	color: inherit;
	text-decoration: underline;
}

/* Dismissable */
.box.dismissable{
	max-height: 100rem;
	padding-right: 3rem;
}
.box.dismissable .close{
	text-decoration: none;
	opacity: 0.5;
	display: block;
	float: right;
	position: relative;
	right: -2rem;
}
.box.dismissable .close:hover{
	text-decoration: none;
	opacity: 1;
}

/* Sizes */
.box.small{
	padding: 1.5rem;
}
.box.small.dismissable{
	padding-right: 2rem
}
.box.small.dismissable .close{
	right: -1.5rem;
}
.box.medium{
	padding: 2.5rem;
}
.box.large{
	padding: 3rem;
}
.box.large.dismissable{
	padding-right: 5rem
}
.box.large.dismissable .close{
	right: -3rem;
}
.box.xlarge{
	padding: 3.5rem;
}
.box.xlarge.dismissable{
	padding-right: 6rem
}
.box.xlarge.dismissable .close{
	right: -3.5rem;
}

/* Child Boxes */
.box .ignore-parent-padding{
	border: none;
}
.box.small > .ignore-parent-padding{
	margin: 0 -1.5rem 0 -1.5rem;
}
.box.medium > .ignore-parent-padding{
	margin: 0 -2.5rem 0 -2.5rem;
}
.box.large > .ignore-parent-padding{
	margin: 0 -3rem 0 -3rem;
}
.box.xlarge > .ignore-parent-padding{
	margin: 0 -3.5rem 0 -3.5rem;
}
.box.small > .ignore-parent-padding.first{
	margin: -1.5rem -1.5rem 3rem -1.5rem;
}
.box.medium > .ignore-parent-padding.first{
	margin: -2.5rem -2.5rem 3rem -2.5rem;
}
.box.large > .ignore-parent-padding.first{
	margin: -3rem -3rem 3rem -3rem;
}
.box.xlarge > .ignore-parent-padding.first{
	margin: -3.5rem -3.5rem 3rem -3.5rem;
}
.box.small > .ignore-parent-padding.last{
	margin: 0 -1.5rem -1.5rem -1.5rem;
}
.box.medium > .ignore-parent-padding.last{
	margin: 0 -2.5rem -2.5rem -2.5rem;
}
.box.large > .ignore-parent-padding.last{
	margin: 0 -3rem -3rem -3rem;
}
.box.xlarge > .ignore-parent-padding.last{
	margin: 0 -3.5rem -3.5rem -3.5rem;
}
.box > .ignore-parent-padding:not(.first):not(.last){
	margin-top: 0;
	margin-bottom: 3rem;
}

/* Styles */
.box.rounded{
	border-radius: 0.3rem;
}
.box.shadow{
	-webkit-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
	   -moz-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
		    box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
}

/*------------------------------------------------------------------
[22. Labels & Highlight]
*/
.label{
	padding: 0 0.8rem;
	border: 1px solid #eeeeee;
}
.label [class*="icon-"]:before{
	margin-left: 0;
	margin-right: 0.5rem;
}
.label,
.highlight{
	background: #eeeeee;
	color: #666666;
}

/* Styles */
.label.rounded{
	border-radius: 0.3rem;
}
.label.pill{
	border-radius: 5rem;
}

/*------------------------------------------------------------------
[23. Charts]
*/
.progress-bar-group{
	margin-bottom: 3rem;
}
.progress-bar-group .progress-bar:last-child{
	margin-bottom: 0;
}
.progress-bar{
	width:100%;
	height:2rem;
	margin-bottom: 2rem;
	font-size: 1.2rem;
	line-height: 1.6rem;
	font-weight: bold;
	text-align: right;
	color:#666666;
	border: 1px solid #eeeeee;
	background: #eeeeee;
	overflow: hidden;
	/* ff cropping fix */
	transform: translate3d(0,0,0);
}
.progress-bar.tmh-perspective{
	overflow: hidden !important;
}
.aux-navigation-active .progress-bar{
	-webkit-mask-image: none;
}
.bar{
	width:100%;
	height:100%;
	padding: 0 1.5rem;
	border: 1px solid #d0d0d0;
	background: #d0d0d0;
	-webkit-backface-visibility: hidden;
}

/* Label */
.progress-bar-label{
	margin-bottom: 0.8rem;
	color: #666;
	font-size: 1.2rem;
	text-align: left;
	display: block;
}
.progress-bar-label + .progress-bar{
	margin-bottom: 1.7rem;
}

/* Sizes */
.progress-bar.small{
	height: 1.3rem;
	font-size: 1rem;
	line-height: 0.8rem;
}
.progress-bar.medium{
	height: 2rem;
}
.progress-bar.large{
	height: 3rem;
	font-size: 1.4rem;
	line-height: 2.6rem;
}
.progress-bar.xlarge{
	height: 4rem;
	font-size: 1.5rem;
	line-height: 3.6rem;
}

/* Syles */
.progress-bar.rounded, 
.progress-bar.rounded .bar{
	border-radius: 0.3rem;
}
.progress-bar.pill, 
.progress-bar.pill .bar{
	border-radius: 10.5rem;
}

/*------------------------------------------------------------------
[24. Range Slider]
*/

.range-slider{
	width: 100%;
	height: 2rem;
	margin-bottom: 2rem;
	font-size: 1.2rem;
	line-height: 1.6rem;
	font-weight: bold;
	text-align: right;
	color:#666666;
	border: 1px solid #eeeeee;
	background: #eeeeee;
	overflow: hidden;
	position: relative;
	/* Safari - Chrome hack for masking issue */
	-webkit-mask-image: url( data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAA5JREFUeNpiYGBgAAgwAAAEAAGbA+oJAAAAAElFTkSuQmCC );
}
.aux-navigation-active .range-slider{
	-webkit-mask-image: none;
}
.range-active-segment{
	width: 100%;
	height: 100%;
	padding: 0 1.5rem;
	border: 1px solid #d0d0d0;
	background: #d0d0d0;
	-webkit-backface-visibility: hidden;
	position: absolute;
	z-index: 0;
}
.range-handle{
	width: 1.8rem;
	height: 1.8rem;
	display: inline-block;
	background: #666;
	cursor: pointer;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	-ms-touch-action: manipulation;
		touch-action: manipulation;
}

/* Sizes */
.range-slider.small{
	height: 1.3rem;
	font-size: 1rem;
	line-height: 0.8rem;
}
.range-slider.small .range-handle{
	width: 1.1rem;
	height: 1.1rem;
}
.range-slider.medium{
	height: 2rem;
}
.range-slider.medium .range-handle{
	width: 1.8rem;
	height: 1.8rem;
}
.range-slider.large{
	height: 3rem;
	font-size: 1.4rem;
	line-height: 2.6rem;
}
.range-slider.large .range-handle{
	width: 2.8rem;
	height: 2.8rem;
}
.range-slider.xlarge{
	height: 4rem;
	font-size: 1.5rem;
	line-height: 3.6rem;
}
.range-slider.xlarge .range-handle{
	width: 3.8rem;
	height: 3.8rem;
}

/* Syles */
.range-slider.rounded, 
.range-slider.rounded .range-active-segment,
.range-slider .range-handle{
	border-radius: 0.3rem;
}
.range-slider.pill, 
.range-slider.pill .range-active-segment,
.range-slider .range-handle{
	border-radius: 10.5rem;
}

/*------------------------------------------------------------------
[25. Video & Audio]
*/
.video-container{
	max-width: 100%;
	overflow: hidden;
}
.video-container,
.audio-container{
	margin-bottom: 3rem;
}
.audio-container .mejs-container{
	height: 3rem;
}
iframe{
	width: 100%;
	border: none;
	overflow: hidden;
	background: #000;
}

/*------------------------------------------------------------------
[26. Dividers]
*/
.divider,
hr {
	margin: 3rem 0;
	border-top: 1px solid #eeeeee;
	border-right: 0;
	border-bottom: 0;
	border-left: 0;
	height: 0;
	clear:both;
}
.divider.dashed,
hr.dashed {
	border-style: dashed;
}
.divider.dotted,
hr.dotted {
	border-style: dotted;
}
.divider.thick,
hr.thick{
	border-top-width: 0.2rem;
}
.divider[class*="border-"],
hr[class*="border-"],
.divider[class*="border-"].dashed,
.divider[class*="border-"].dotted,
hr[class*="border-"].dashed,
hr[class*="border-"].dotted{
	border-right-style: none !important;
	border-bottom-style: none !important;
	border-left-style: none !important;
}

/*------------------------------------------------------------------
[27. Typography]
*/
h1, h2, h3, h4, h5, h6{
	color: #333333;
	font-weight: bold;
	line-height: 1;
	margin: 0 0 2rem 0;
}
h1 {
	font-size: 4.8rem;
}
h2 {
	font-size: 3.2rem;
}
h3 {
	font-size: 2.4rem;
}
h3.widget-title {
	font-size: 2.1rem;
}
h4 {
	font-size: 2.1rem;
}
h5 {
	font-size: 1.8rem;
}
h6 {
	font-size: 1.6rem;
}
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a{
	font-size: inherit;
	font-weight: inherit;
	line-height: inherit;
}
h1 a:hover, 
h2 a:hover, 
h3 a:hover, 
h4 a:hover, 
h5 a:hover, 
h6 a:hover {
    color: inherit;
}
p {
	margin: 0 0 3rem 0;
}
h1[class*="border-"],
h1[class*="bkg-"],
h1 span[class*="border-"],
h1 span[class*="bkg-"],
h2[class*="border-"],
h2[class*="bkg-"],
h2 span[class*="border-"],
h2 span[class*="bkg-"],
h3[class*="border-"],
h3[class*="bkg-"],
h3 span[class*="border-"],
h3 span[class*="bkg-"],
h4[class*="border-"],
h4[class*="bkg-"],
h4 span[class*="border-"],
h4 span[class*="bkg-"],
h5[class*="border-"],
h5[class*="bkg-"],
h5 span[class*="border-"],
h5 span[class*="bkg-"],
h6[class*="border-"],
h6[class*="bkg-"],
h6 span[class*="border-"],
h6 span[class*="bkg-"],
p[class*="border-"],
p[class*="bkg-"]{
	padding: 0.4rem 0.6rem;
}
address{
	margin: 0 0 3rem 0;
}
.lead{
	margin-bottom: 3rem;
	font-size: 2.2rem;
	line-height: 1.5;
	font-weight: 200;
}
.separator{
	width: 10rem;
	height: 0.4rem;
	display: block;
	background-color: #333333;
	margin-bottom: 2rem;
}
.separator.small{
	width: 7rem;
	height: 0.2rem;
}
.separator.large{
	width: 13rem;
	height: 0.6rem;
}
.separator.xlarge{
	width: 16rem;
	height: 0.8rem;
}
.separator.full-width{
	width: 100%;
}
.center .separator,
.separator.center{
	margin-right: auto;
	margin-left: auto;
}
.right .separator,
.separator.right{
	margin-left: auto;
}

/* Font Alternatives */
.font-alt-1{
	font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
}
.font-alt-1{
	font-family: "Times New Roman", Arial, sans-serif;
}

/* Styles */
em {
	font-style: italic;
}
strong {
	font-weight: bold;
}
small {
	font-size: 65%;
}
[class*="weight-"] p,
[class*="weight-"] span, 
[class*="weight-"] a{
	font-weight: inherit;
}
.weight-light{
	font-weight: 100;
}
.weight-thin{
	font-weight: 200;
}
.weight-regular{
	font-weight: 400;
}
.weight-semi-bold{
	font-weight: 600;
}
.weight-bold{
	font-weight: 700;
}
.weight-xbold{
	font-weight: 900;
}
.text-shadow{
	text-shadow: 0.2rem 0.2rem 0.2rem rgba(0,0,0,0.5);
}
[class*="lspacing-"] p,
[class*="lspacing-"] span, 
[class*="lspacing-"] a{
	letter-spacing: inherit;
}
.lspacing-small{
	letter-spacing: 0.2rem;
}
.lspacing-medium{
	letter-spacing: 0.3rem;
}
.lspacing-large{
	letter-spacing: 0.5rem;
}
.lspacing-xlarge{
	letter-spacing: 0.8rem;
}

/* Links */
a, p a {
	color: #666666;
	text-decoration: none;
	outline: none;
}
a:hover, 
p a:hover{
	color: #333333;
	text-decoration: none;
}

/* Link announcement */
a.announcement{
	padding: 1rem 2rem;
	line-height: inherit;
	display: inline-flex;
	align-items: center;
}
a.announcement.rounded,
a.announcement.rounded .label{
	border-radius: 0.3rem;
}
a.announcement.pill,
a.announcement.pill .label{
	border-radius: 5rem;
}
a.announcement .label{
	border-radius: inherit;
}
a.announcement.left .label{
	margin-right: 1rem;
}
a.announcement.right .label{
	margin-left: 1rem;
}
a.announcement .messaage{
	flex: 1;
}

/* Text Highlight */
::selection {
	background: #333333;
	color: #ffffff;
}
::-moz-selection {
	background: #333333;
	color: #ffffff;
}

/*------------------------------------------------------------------
[28. Blockquotes]
*/
blockquote{
	margin: 0 0 3rem 0;
	font-size: 1.8rem;
	line-height: 1.5;
} 
blockquote p{
	margin-bottom: 0;
	font-size: 1.8rem;
}
blockquote cite, 
blockquote .cite{
	margin-top: 2rem;
	font-size:60%;
	display: block; 
}

/* Sizes */
blockquote.small, 
blockquote.small p {
	font-size: 1.6rem;
}
blockquote.medium, 
blockquote.medium p {
	font-size: 1.8rem;
}
blockquote.large, 
blockquote.large p {
	font-size: 2.1rem;
}
blockquote.xlarge, 
blockquote.xlarge p {
	font-size: 2.4rem;
}

/* Styles */
blockquote.border{
	padding: 0 0 0 2.5rem;
	border-left: 1px solid #eeeeee;
}
blockquote.border.small{
	padding-left: 2rem;
}
blockquote.border.medium {
	padding-left: 2.5rem;
}
blockquote.border.large{
	padding-left: 3rem;
}
blockquote.border.xlarge{
	padding-left: 3.5rem;
}
blockquote.border.small{
	padding-right: 2rem;
}
blockquote.border.medium {
	padding-right: 2.5rem;
}
blockquote.border.large{
	padding-right: 3rem;
}
blockquote.border.xlarge{
	padding-right: 3.5rem;
}
blockquote.icon [class*="icon-"]{
	font-size: 150%;
	text-align: center;
}
blockquote.avatar span{
	margin-bottom: 1.5rem;
}
blockquote.avatar span, 
blockquote.avatar span img{
	width: 5rem;
	height: 5rem;
	border-radius: 7rem;
	display: inline-block;
}
blockquote.avatar.small span, 
blockquote.avatar.small span img{
	width: 4rem;
	height: 4rem;	
}
blockquote.avatar.medium span, 
blockquote.avatar.medium span img{
	width: 5rem;
	height: 5rem;
}
blockquote.avatar.large span, 
blockquote.avatar.large span img{
	width: 6rem;
	height: 6rem;
}
blockquote.avatar.xlarge span, 
blockquote.avatar.xlarge span img{
	width: 7rem;
	height: 7rem;
}

/* Alignment */
blockquote.pull-left{
	width: 40%;
	float: left;
	margin-right: 2rem;
	margin-bottom: 2rem;
}
blockquote.pull-right{
	width: 40%;
	float: right;
	margin-left: 2rem;
	margin-bottom: 2rem;
}
blockquote.center{
	margin-left: auto;
	margin-right: auto;
}
blockquote.border.right{
	padding: 0 2.5rem 0 0;
	border-left: none;
	border-right: 1px solid #eeeeee;
}
blockquote.border.center{
	margin-left: auto;
	margin-right: auto;
	padding: 2.5rem 0 0 0;
	border-left: none;
	border-top: 1px solid #eeeeee;
}

/*------------------------------------------------------------------
[29. Breadcrumb]
*/
.breadcrumb { 
	margin-left: 0;
	list-style: none;
	overflow: hidden;
}
.breadcrumb li { 
	float: left; 
}
.breadcrumb li,
.breadcrumb li a {
	font-size: 1.2rem;
}
.breadcrumb li:after,
.breadcrumb li:before{
	padding-left: 1.2rem;
	padding-right: 1.2rem;
	font-family: 'icomoon';
}
.breadcrumb li:after{
	content: "\e640";
}
.breadcrumb.center li{
	display: inline-block;
	float: none;
}
.breadcrumb.right li:after{
	padding: 0;
	content: "";
}
.breadcrumb.right li:before{
	content: "\e63f";
}
.breadcrumb li:last-child:after,
.breadcrumb.right li:first-child:before{
	content: "";
	padding: 0;
}

/*------------------------------------------------------------------
[30. Code & Pre]
*/
code, samp, kbd {
	font-family: "Courier New", Courier, monospace, sans-serif;
	text-align: left;
}
pre{
	max-width: 100%;
	margin: 0 0 3rem 0;
	white-space: pre;
	word-break: break-all;
	word-wrap: break-word;
}
pre code{
	line-height: 1.5;
	white-space: pre;
}
.tag{
	color: #074a72;
}
.tag .att{
	color: #47a4db;
}
.tag .val{
	color: #d74950;
}
.tag .comm{
	color: #999999;
}

/*------------------------------------------------------------------
[31. Font Icons]
*/

@font-face {
	font-family: 'icomoon';
	src:url('../fonts/entypo.eot');
	src:url('../fonts/entypo.eot') format('embedded-opentype'),
		url('../fonts/entypo.woff') format('woff'),
		url('../fonts/entypo.ttf') format('truetype'),
		url('../fonts/entypo.svg') format('svg');
	font-weight: normal;
	font-style: normal;
}
[class^="icon-"], 
[class*=" icon-"] {
	font-family: 'icomoon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	display: inline-block;

	/* Better Font Rendering */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
[class*="icon-"] + [class*="icon-"]{
	margin-left: 1rem;
}

.icon-add-to-list:before {content: "\e600";}
.icon-add-user:before {content: "\e601";}
.icon-address:before {content: "\e602";}
.icon-adjust:before {content: "\e603";}
.icon-air:before {content: "\e604";}
.icon-aircraft-landing:before {content: "\e605";}
.icon-aircraft-take-off:before {content: "\e606";}
.icon-aircraft:before {content: "\e607";}
.icon-align-bottom:before {content: "\e608";}
.icon-align-horizontal-middle:before {content: "\e609";}
.icon-align-left:before {content: "\e60a";}
.icon-align-right:before {content: "\e60b";}
.icon-align-top:before {content: "\e60c";}
.icon-align-vertical-middle:before {content: "\e60d";}
.icon-archive:before {content: "\e60e";}
.icon-area-graph:before {content: "\e60f";}
.icon-arrow-bold-down:before {content: "\e610";}
.icon-arrow-bold-left:before {content: "\e611";}
.icon-arrow-bold-right:before {content: "\e612";}
.icon-arrow-bold-up:before {content: "\e613";}
.icon-arrow-down:before {content: "\e614";}
.icon-arrow-left:before {content: "\e615";}
.icon-arrow-long-down:before {content: "\e616";}
.icon-arrow-long-left:before {content: "\e617";}
.icon-arrow-long-right:before {content: "\e618";}
.icon-arrow-long-up:before {content: "\e619";}
.icon-arrow-right:before {content: "\e61a";}
.icon-arrow-up:before {content: "\e61b";}
.icon-arrow-with-circle-down:before {content: "\e61c";}
.icon-arrow-with-circle-left:before {content: "\e61d";}
.icon-arrow-with-circle-right:before {content: "\e61e";}
.icon-arrow-with-circle-up:before {content: "\e61f";}
.icon-attachment:before {content: "\e620";}
.icon-awareness-ribbon:before {content: "\e621";}
.icon-back-in-time:before {content: "\e622";}
.icon-back:before {content: "\e623";}
.icon-bar-graph:before {content: "\e624";}
.icon-battery:before {content: "\e625";}
.icon-beamed-note:before {content: "\e626";}
.icon-bell:before {content: "\e627";}
.icon-blackboard:before {content: "\e628";}
.icon-block:before {content: "\e629";}
.icon-book:before {content: "\e62a";}
.icon-bookmark:before {content: "\e62b";}
.icon-bookmarks:before {content: "\e62c";}
.icon-bowl:before {content: "\e62d";}
.icon-box:before {content: "\e62e";}
.icon-briefcase:before {content: "\e62f";}
.icon-browser:before {content: "\e630";}
.icon-brush:before {content: "\e631";}
.icon-bucket:before {content: "\e632";}
.icon-bug:before {content: "\e633";}
.icon-cake:before {content: "\e634";}
.icon-calculator:before {content: "\e635";}
.icon-calendar:before {content: "\e636";}
.icon-camera:before {content: "\e637";}
.icon-ccw:before {content: "\e638";}
.icon-chat:before {content: "\e639";}
.icon-check:before {content: "\e63a";}
.icon-down:before {content: "\e63b";}
.icon-left:before {content: "\e63c";}
.icon-right:before {content: "\e63d";}
.icon-down-open-mini:before {content: "\e63e";}
.icon-left-open-mini:before {content: "\e63f";}
.icon-right-open-mini:before {content: "\e640";}
.icon-up-open-mini:before {content: "\e641";}
.icon-down-open-big:before {content: "\e642";}
.icon-left-open-big:before {content: "\e643";}
.icon-right-open-big:before {content: "\e644";}
.icon-up-open-big:before {content: "\e645";}
.icon-up:before {content: "\e646";}
.icon-down-circled:before {content: "\e647";}
.icon-left-circled:before {content: "\e648";}
.icon-right-circled:before {content: "\e649";}
.icon-up-circled:before {content: "\e64a";}
.icon-circle-with-cross:before {content: "\e64b";}
.icon-circle-with-minus:before {content: "\e64c";}
.icon-circle-with-plus:before {content: "\e64d";}
.icon-circle:before {content: "\e64e";}
.icon-circular-graph:before {content: "\e64f";}
.icon-clapperboard:before {content: "\e650";}
.icon-classic-computer:before {content: "\e651";}
.icon-clipboard:before {content: "\e652";}
.icon-clock:before {content: "\e653";}
.icon-cloud:before {content: "\e654";}
.icon-code:before {content: "\e655";}
.icon-cog:before {content: "\e656";}
.icon-colours:before {content: "\e657";}
.icon-compass:before {content: "\e658";}
.icon-fast-backward:before {content: "\e659";}
.icon-fast-forward:before {content: "\e65a";}
.icon-jump-to-start:before {content: "\e65b";}
.icon-next:before {content: "\e65c";}
.icon-paus:before {content: "\e65d";}
.icon-play:before {content: "\e65e";}
.icon-record:before {content: "\e65f";}
.icon-stop:before {content: "\e660";}
.icon-volume:before {content: "\e661";}
.icon-copy:before {content: "\e662";}
.icon-creative-commons-attribution:before {content: "\e663";}
.icon-creative-commons-noderivs:before {content: "\e664";}
.icon-creative-commons-noncommercial-eu:before {content: "\e665";}
.icon-creative-commons-noncommercial-us:before {content: "\e666";}
.icon-creative-commons-public-domain:before {content: "\e667";}
.icon-creative-commons-remix:before {content: "\e668";}
.icon-creative-commons-share:before {content: "\e669";}
.icon-creative-commons-sharealike:before {content: "\e66a";}
.icon-creative-commons:before {content: "\e66b";}
.icon-credit-card:before {content: "\e66c";}
.icon-credit:before {content: "\e66d";}
.icon-crop:before {content: "\e66e";}
.icon-cancel:before {content: "\e66f";}
.icon-cup:before {content: "\e670";}
.icon-cw:before {content: "\e671";}
.icon-cycle:before {content: "\e672";}
.icon-database:before {content: "\e673";}
.icon-dial-pad:before {content: "\e674";}
.icon-direction:before {content: "\e675";}
.icon-document-landscape:before {content: "\e676";}
.icon-document:before {content: "\e677";}
.icon-documents:before {content: "\e678";}
.icon-dot-single:before {content: "\e679";}
.icon-dots-three-horizontal:before {content: "\e67a";}
.icon-dots-three-vertical:before {content: "\e67b";}
.icon-dots-two-horizontal:before {content: "\e67c";}
.icon-dots-two-vertical:before {content: "\e67d";}
.icon-download:before {content: "\e67e";}
.icon-drink:before {content: "\e67f";}
.icon-drive:before {content: "\e680";}
.icon-drop:before {content: "\e681";}
.icon-edit:before {content: "\e682";}
.icon-email:before {content: "\e683";}
.icon-emoji-flirt:before {content: "\e684";}
.icon-emoji-happy:before {content: "\e685";}
.icon-emoji-neutral:before {content: "\e686";}
.icon-emoji-sad:before {content: "\e687";}
.icon-erase:before {content: "\e688";}
.icon-eraser:before {content: "\e689";}
.icon-export:before {content: "\e68a";}
.icon-eye-with-line:before {content: "\e68b";}
.icon-eye:before {content: "\e68c";}
.icon-feather:before {content: "\e68d";}
.icon-fingerprint:before {content: "\e68e";}
.icon-flag:before {content: "\e68f";}
.icon-flash:before {content: "\e690";}
.icon-flashlight:before {content: "\e691";}
.icon-flat-brush:before {content: "\e692";}
.icon-flow-branch:before {content: "\e693";}
.icon-flow-cascade:before {content: "\e694";}
.icon-flow-line:before {content: "\e695";}
.icon-flow-parallel:before {content: "\e696";}
.icon-flow-tree:before {content: "\e697";}
.icon-flower:before {content: "\e698";}
.icon-folder-images:before {content: "\e699";}
.icon-folder-music:before {content: "\e69a";}
.icon-folder-video:before {content: "\e69b";}
.icon-folder:before {content: "\e69c";}
.icon-forward:before {content: "\e69d";}
.icon-funnel:before {content: "\e69e";}
.icon-game-controller:before {content: "\e69f";}
.icon-gauge:before {content: "\e6a0";}
.icon-globe:before {content: "\e6a1";}
.icon-graduation-cap:before {content: "\e6a2";}
.icon-grid:before {content: "\e6a3";}
.icon-hair-cross:before {content: "\e6a4";}
.icon-hand:before {content: "\e6a5";}
.icon-heart-outlined:before {content: "\e6a6";}
.icon-heart:before {content: "\e6a7";}
.icon-help-with-circle:before {content: "\e6a8";}
.icon-help:before {content: "\e6a9";}
.icon-home:before {content: "\e6aa";}
.icon-hour-glass:before {content: "\e6ab";}
.icon-image-inverted:before {content: "\e6ac";}
.icon-image:before {content: "\e6ad";}
.icon-images:before {content: "\e6ae";}
.icon-inbox:before {content: "\e6af";}
.icon-infinity:before {content: "\e6b0";}
.icon-info-with-circle:before {content: "\e6b1";}
.icon-info:before {content: "\e6b2";}
.icon-install:before {content: "\e6b3";}
.icon-key:before {content: "\e6b4";}
.icon-keyboard:before {content: "\e6b5";}
.icon-lab-flask:before {content: "\e6b6";}
.icon-landline:before {content: "\e6b7";}
.icon-language:before {content: "\e6b8";}
.icon-laptop:before {content: "\e6b9";}
.icon-layers:before {content: "\e6ba";}
.icon-leaf:before {content: "\e6bb";}
.icon-level-down:before {content: "\e6bc";}
.icon-level-up:before {content: "\e6bd";}
.icon-lifebuoy:before {content: "\e6be";}
.icon-light-bulb:before {content: "\e6bf";}
.icon-light-down:before {content: "\e6c0";}
.icon-light-up:before {content: "\e6c1";}
.icon-line-graph:before {content: "\e6c2";}
.icon-link:before {content: "\e6c3";}
.icon-list:before {content: "\e6c4";}
.icon-location-pin:before {content: "\e6c5";}
.icon-location:before {content: "\e6c6";}
.icon-lock-open:before {content: "\e6c7";}
.icon-lock:before {content: "\e6c8";}
.icon-log-out:before {content: "\e6c9";}
.icon-login:before {content: "\e6ca";}
.icon-loop:before {content: "\e6cb";}
.icon-magnet:before {content: "\e6cc";}
.icon-magnifying-glass:before {content: "\e6cd";}
.icon-mail:before {content: "\e6ce";}
.icon-man:before {content: "\e6cf";}
.icon-map:before {content: "\e6d0";}
.icon-mask:before {content: "\e6d1";}
.icon-medal:before {content: "\e6d2";}
.icon-megaphone:before {content: "\e6d3";}
.icon-menu:before {content: "\e6d4";}
.icon-merge:before {content: "\e6d5";}
.icon-message:before {content: "\e6d6";}
.icon-mic:before {content: "\e6d7";}
.icon-minus:before {content: "\e6d8";}
.icon-mobile:before {content: "\e6d9";}
.icon-modern-mic:before {content: "\e6da";}
.icon-moon:before {content: "\e6db";}
.icon-mouse-pointer:before {content: "\e6dc";}
.icon-mouse:before {content: "\e6dd";}
.icon-music:before {content: "\e6de";}
.icon-network:before {content: "\e6df";}
.icon-new-message:before {content: "\e6e0";}
.icon-new:before {content: "\e6e1";}
.icon-news:before {content: "\e6e2";}
.icon-newsletter:before {content: "\e6e3";}
.icon-note:before {content: "\e6e4";}
.icon-notification:before {content: "\e6e5";}
.icon-notifications-off:before {content: "\e6e6";}
.icon-old-mobile:before {content: "\e6e7";}
.icon-old-phone:before {content: "\e6e8";}
.icon-open-book:before {content: "\e6e9";}
.icon-palette:before {content: "\e6ea";}
.icon-paper-plane:before {content: "\e6eb";}
.icon-pencil:before {content: "\e6ec";}
.icon-phone:before {content: "\e6ed";}
.icon-pie-chart:before {content: "\e6ee";}
.icon-pin:before {content: "\e6ef";}
.icon-plus:before {content: "\e6f0";}
.icon-popup:before {content: "\e6f1";}
.icon-power-plug:before {content: "\e6f2";}
.icon-price-ribbon:before {content: "\e6f3";}
.icon-price-tag:before {content: "\e6f4";}
.icon-print:before {content: "\e6f5";}
.icon-progress-empty:before {content: "\e6f6";}
.icon-progress-full:before {content: "\e6f7";}
.icon-progress-one:before {content: "\e6f8";}
.icon-progress-two:before {content: "\e6f9";}
.icon-publish:before {content: "\e6fa";}
.icon-quote:before {content: "\e6fb";}
.icon-radio:before {content: "\e6fc";}
.icon-remove-user:before {content: "\e6fd";}
.icon-reply-all:before {content: "\e6fe";}
.icon-reply:before {content: "\e6ff";}
.icon-resize-100:before {content: "\e700";}
.icon-resize-full-screen:before {content: "\e701";}
.icon-retweet:before {content: "\e702";}
.icon-rocket:before {content: "\e703";}
.icon-round-brush:before {content: "\e704";}
.icon-rss:before {content: "\e705";}
.icon-ruler:before {content: "\e706";}
.icon-save:before {content: "\e707";}
.icon-scissors:before {content: "\e708";}
.icon-select-arrows:before {content: "\e709";}
.icon-share-alternative:before {content: "\e70a";}
.icon-share:before {content: "\e70b";}
.icon-shareable:before {content: "\e70c";}
.icon-shield:before {content: "\e70d";}
.icon-shop:before {content: "\e70e";}
.icon-shopping-bag:before {content: "\e70f";}
.icon-shopping-basket:before {content: "\e710";}
.icon-shopping-cart:before {content: "\e711";}
.icon-shuffle:before {content: "\e712";}
.icon-signal:before {content: "\e713";}
.icon-sound-mix:before {content: "\e714";}
.icon-sound-mute:before {content: "\e715";}
.icon-sound:before {content: "\e716";}
.icon-sports-club:before {content: "\e717";}
.icon-spreadsheet:before {content: "\e718";}
.icon-squared-cross:before {content: "\e719";}
.icon-squared-minus:before {content: "\e71a";}
.icon-squared-plus:before {content: "\e71b";}
.icon-star-outlined:before {content: "\e71c";}
.icon-star:before {content: "\e71d";}
.icon-stopwatch:before {content: "\e71e";}
.icon-suitcase:before {content: "\e71f";}
.icon-swap:before {content: "\e720";}
.icon-sweden:before {content: "\e721";}
.icon-switch:before {content: "\e722";}
.icon-tablet-mobile-combo:before {content: "\e723";}
.icon-tablet:before {content: "\e724";}
.icon-tag:before {content: "\e725";}
.icon-text-document-inverted:before {content: "\e726";}
.icon-text-document:before {content: "\e727";}
.icon-text:before {content: "\e728";}
.icon-thermometer:before {content: "\e729";}
.icon-thumbs-down:before {content: "\e72a";}
.icon-thumbs-up:before {content: "\e72b";}
.icon-thunder-cloud:before {content: "\e72c";}
.icon-ticket:before {content: "\e72d";}
.icon-time-slot:before {content: "\e72e";}
.icon-tools:before {content: "\e72f";}
.icon-traffic-cone:before {content: "\e730";}
.icon-trash:before {content: "\e731";}
.icon-tree:before {content: "\e732";}
.icon-triangle-down:before {content: "\e733";}
.icon-triangle-left:before {content: "\e734";}
.icon-triangle-right:before {content: "\e735";}
.icon-triangle-up:before {content: "\e736";}
.icon-trophy:before {content: "\e737";}
.icon-tv:before {content: "\e738";}
.icon-typing:before {content: "\e739";}
.icon-uninstall:before {content: "\e73a";}
.icon-unread:before {content: "\e73b";}
.icon-untag:before {content: "\e73c";}
.icon-upload-to-cloud:before {content: "\e73d";}
.icon-upload:before {content: "\e73e";}
.icon-user:before {content: "\e73f";}
.icon-users:before {content: "\e740";}
.icon-v-card:before {content: "\e741";}
.icon-video-camera:before {content: "\e742";}
.icon-video:before {content: "\e743";}
.icon-vinyl:before {content: "\e744";}
.icon-voicemail:before {content: "\e745";}
.icon-wallet:before {content: "\e746";}
.icon-warning:before {content: "\e747";}
.icon-water:before {content: "\e748";}
.icon-px-with-circle:before {content: "\e749";}
.icon-px:before {content: "\e74a";}
.icon-app-store:before {content: "\e74b";}
.icon-baidu:before {content: "\e74c";}
.icon-basecamp:before {content: "\e74d";}
.icon-behance:before {content: "\e74e";}
.icon-creative-cloud:before {content: "\e74f";}
.icon-dribbble-with-circle:before {content: "\e750";}
.icon-dribbble:before {content: "\e751";}
.icon-dropbox:before {content: "\e752";}
.icon-evernote:before {content: "\e753";}
.icon-facebook-with-circle:before {content: "\e754";}
.icon-facebook:before {content: "\e755";}
.icon-flattr:before {content: "\e756";}
.icon-flickr-with-circle:before {content: "\e757";}
.icon-flickr:before {content: "\e758";}
.icon-foursquare:before {content: "\e759";}
.icon-github-with-circle:before {content: "\e75a";}
.icon-github:before {content: "\e75b";}
.icon-google-drive:before {content: "\e75c";}
.icon-google-hangouts:before {content: "\e75d";}
.icon-google-play:before {content: "\e75e";}
.icon-google-with-circle:before {content: "\e75f";}
.icon-google:before {content: "\e760";}
.icon-grooveshark:before {content: "\e761";}
.icon-houzz:before {content: "\e762";}
.icon-icloud:before {content: "\e763";}
.icon-instagram-with-circle:before {content: "\e764";}
.icon-instagram:before {content: "\e765";}
.icon-lastfm-with-circle:before {content: "\e766";}
.icon-lastfm:before {content: "\e767";}
.icon-linkedin-with-circle:before {content: "\e768";}
.icon-linkedin:before {content: "\e769";}
.icon-mail-with-circle:before {content: "\e76a";}
.icon-medium-with-circle:before {content: "\e76b";}
.icon-medium:before {content: "\e76c";}
.icon-mixi:before {content: "\e76d";}
.icon-onedrive:before {content: "\e76e";}
.icon-paypal:before {content: "\e76f";}
.icon-picasa:before {content: "\e770";}
.icon-pinterest-with-circle:before {content: "\e771";}
.icon-pinterest:before {content: "\e772";}
.icon-qq-with-circle:before {content: "\e773";}
.icon-qq:before {content: "\e774";}
.icon-raft-with-circle:before {content: "\e775";}
.icon-raft:before {content: "\e776";}
.icon-rainbow:before {content: "\e777";}
.icon-rdio-with-circle:before {content: "\e778";}
.icon-rdio:before {content: "\e779";}
.icon-renren:before {content: "\e77a";}
.icon-scribd:before {content: "\e77b";}
.icon-sina-weibo:before {content: "\e77c";}
.icon-skype-with-circle:before {content: "\e77d";}
.icon-skype:before {content: "\e77e";}
.icon-slideshare:before {content: "\e77f";}
.icon-smashing:before {content: "\e780";}
.icon-soundcloud:before {content: "\e781";}
.icon-spotify-with-circle:before {content: "\e782";}
.icon-spotify:before {content: "\e783";}
.icon-stumbleupon-with-circle:before {content: "\e784";}
.icon-stumbleupon:before {content: "\e785";}
.icon-swarm:before {content: "\e786";}
.icon-tripadvisor:before {content: "\e787";}
.icon-tumblr-with-circle:before {content: "\e788";}
.icon-tumblr:before {content: "\e789";}
.icon-twitter-with-circle:before {content: "\e78a";}
.icon-twitter:before {content: "\e78b";}
.icon-vimeo-with-circle:before {content: "\e78c";}
.icon-vimeo:before {content: "\e78d";}
.icon-vine-with-circle:before {content: "\e78e";}
.icon-vine:before {content: "\e78f";}
.icon-vk-alternitive:before {content: "\e790";}
.icon-vk-with-circle:before {content: "\e791";}
.icon-vk:before {content: "\e792";}
.icon-windows-store:before {content: "\e793";}
.icon-xing-with-circle:before {content: "\e794";}
.icon-xing:before {content: "\e795";}
.icon-yelp:before {content: "\e796";}
.icon-youko-with-circle:before {content: "\e797";}
.icon-youko:before {content: "\e798";}
.icon-youtube-with-circle:before {content: "\e799";}
.icon-youtube:before {content: "\e79a";}

/* Sizes */
[class*="icon-"].small{
	font-size: 1.6rem;
}
[class*="icon-"].medium{
	font-size: 2.4rem;
}
[class*="icon-"].large{
	font-size: 3.2rem;
}
[class*="icon-"].xlarge{
	font-size: 3.8rem;
}
.icon-boxed,
.icon-circled{
	width: 6.6rem;
	height: 6.6rem;
	margin-bottom: 0.4rem;
	line-height: 6.6rem;
	text-align: center;
	border: 1px solid #eeeeee;
	overflow: hidden;
	position: relative;
}
.icon-boxed.small,
.icon-circled.small{
	width: 5.8rem;
	height: 5.8rem;
	line-height: 5.6rem;
}
.icon-boxed.medium,
.icon-circled.medium{
	width: 6.6rem;
	height: 6.6rem;
	line-height: 6.4rem;
}
.icon-boxed.large,
.icon-circled.large{
	width: 7.4rem;
	height: 7.4rem;
	line-height: 7.2rem;
}
.icon-boxed.xlarge,
.icon-circled.xlarge{
	width: 9rem;
	height: 9rem;
	line-height: 8.8rem;
}
.icon-boxed.left + span,
.icon-circled.left + span{
	margin-left: 0.5rem;
}
.icon-boxed.right + span,
.icon-circled.right + span{
	margin-right: 0.5rem;
}

/* Styles */
.icon-boxed.rounded{
	border-radius: 0.3rem;
}
.icon-circled{
	border-radius: 10rem;
}
.icon-boxed.shadow,
.icon-circled.shadow{
	-webkit-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
	   -moz-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
		    box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
}

/* Animations */
.icon-boxed.pulsate,
.icon-circled.pulsate{
	overflow: visible;
}
.icon-boxed.pulsate:after,
.icon-circled.pulsate:after {
	width: inherit;
	height: inherit;
	padding: inherit;
	content: "";
	border: inherit;
	border-radius: inherit;
	position: absolute;
	top: -1px;
	left: -1px;
}
.icon-boxed.thick.pulsate:after,
.icon-circled.thick.pulsate:after{
	top: -2px;
	left: -2px;
}
.icon-boxed.pulsate:after,
.icon-circled.pulsate:after{
	-webkit-animation: pulsate 1s ease-out;
	-webkit-animation-iteration-count: infinite; 
			animation: pulsate 1s ease-out;
			animation-iteration-count: infinite; 
	opacity: 0;
}
.icon-boxed.slide-left-right:before,
.icon-circled.slide-left-right:before{
	-webkit-animation: slide-left-right 6s ease-in-out;
	-webkit-animation-iteration-count: infinite; 
			animation: slide-left-right 6s ease-in-out;
			animation-iteration-count: infinite; 
	display: block;
}
.icon-boxed.slide-top-bottom:before,
.icon-circled.slide-top-bottom:before{
	-webkit-animation: slide-top-bottom 6s ease-in-out;
	-webkit-animation-iteration-count: infinite; 
			animation: slide-top-bottom 6s ease-in-out;
			animation-iteration-count: infinite; 
	display: block;
}
.icon-boxed.scale-in:before,
.icon-circled.scale-in:before{
	-webkit-animation: scale-in 6s ease-in-out;
	-webkit-animation-iteration-count: infinite; 
			animation: scale-in 6s ease-in-out;
			animation-iteration-count: infinite; 
	display: block;
}
.icon-boxed.scale-out:before,
.icon-circled.scale-out:before{
	-webkit-animation: scale-out 6s ease-in-out;
	-webkit-animation-iteration-count: infinite; 
			animation: scale-out 6s ease-in-out;
			animation-iteration-count: infinite; 
	display: block;
}

/* pulsate */
@-webkit-keyframes pulsate {
	0% {
		-webkit-transform: scale(0.6, 0.6); 
		opacity: 0;
	}
	50% {
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(1.2, 1.2);
		opacity: 0;
	}
}
@keyframes pulsate {
	0% {
		transform: scale(0.6, 0.6); 
		opacity: 0;
	}
	50% {
		opacity: 1;
	}
	100% {
		transform: scale(1.2, 1.2);
		opacity: 0;
	}
}
/* left/right */
@-webkit-keyframes slide-left-right {
	15%,100% {
		-webkit-transform: translateX(0); 
		opacity: 1;
	}
	5% {
		-webkit-transform: translateX(5rem); opacity: 0;
	}
	10% {
		-webkit-transform: translateX(-5rem); opacity: 0;
	}
}
@keyframes slide-left-right {
	15%,100% {
		transform: translateX(0); 
		opacity: 1;
	}
	5% {
		transform: translateX(5rem); opacity: 0;
	}
	10% {
		transform: translateX(-5rem); opacity: 0;
	}
}
/* top/bottom */
@-webkit-keyframes slide-top-bottom {
	15%,100% {
		-webkit-transform: translateY(0); 
		opacity: 1;
	}
	5% {
		-webkit-transform: translateY(5rem); opacity: 0;
	}
	10% {
		-webkit-transform: translateY(-5rem); opacity: 0;
	}
}
@keyframes slide-top-bottom {
	15%,100% {
		transform: translateY(0); 
		opacity: 1;
	}
	5% {
		transform: translateY(5rem); opacity: 0;
	}
	10% {
		transform: translateY(-5rem); opacity: 0;
	}
}
/* scale in*/
@-webkit-keyframes scale-in {
	15%,100% {
		-webkit-transform: scale(1); 
		opacity: 1;
	}
	5% {
		-webkit-transform: scale(0.6); opacity: 0;
	}
	10% {
		-webkit-transform: scale(1.2); opacity: 0;
	}
}
@keyframes scale-in {
	15%,100% {
		transform: scale(1); 
		opacity: 1;
	}
	5% {
		transform: scale(0.6); opacity: 0;
	}
	10% {
		transform: scale(1.2); opacity: 0;
	}
}
/* scale out*/
@-webkit-keyframes scale-out {
	15%,100% {
		-webkit-transform: scale(1); 
		opacity: 1;
	}
	5% {
		-webkit-transform: scale(1.2); opacity: 0;
	}
	10% {
		-webkit-transform: scale(0.6); opacity: 0;
	}
}
@keyframes scale-out {
	15%,100% {
		transform: scale(1); 
		opacity: 1;
	}
	5% {
		transform: scale(1.2); opacity: 0;
	}
	10% {
		transform: scale(0.6); opacity: 0;
	}
}

/*------------------------------------------------------------------
[32. Color Classes]
*/

/* 
	Only applicable for: 
	buttons, labels, boxes, feature columns, feature icons, 
	progress bars, text fields and textareas.
*/

/* Padding */
/* Border/Bkg padding */
[class*="border-"].padded,
[class*="bkg-"].padded{
	padding: 1rem 2rem;
}

/* Border Color Classes */
[class*="border-"]{
	border: 1px solid #eee;
}
[class*="border-"]:not([class*="bkg-"]),
[class*="border-"][class*="bkg-hover-"],
[class*="border-hover-"]:hover{
	background: none;
	background: rgba(0,0,0,0);
}
/* Styles */
[class*="border-"].solid{
	border-style: solid;
	border-width: 1px;
}
[class*="border-"].dashed{
	border-style: dashed;
	border-width: 1px;
}
[class*="border-"].dotted{
	border-style: dotted;
	border-width: 1px;
}
[class*="border-"].thick, 
[class*="border-"].thick *{
	border-width: 0.2rem;
}
.border-turquoise{border-color: #00a186;}
.border-hover-turquoise:hover{border-color: #008b73;}
.border-turquoise-light{border-color: #00bd9c;}
.border-hover-turquoise-light:hover{border-color: #00a186; }
.border-green{border-color: #1bb05d; color: #1bb05d;}
.border-hover-green:hover{border-color: #179851;}
.border-green-light{border-color: #1ecd6d;}
.border-hover-green-light:hover{border-color: #1bb05d;}
.border-blue{border-color: #227fbb;}
.border-hover-blue:hover{border-color: #1d6ea3;}
.border-blue-light{border-color: #2c96dd;}
.border-hover-blue-light:hover{border-color: #227fbb;}
.border-navy{border-color:#2c3e51;}
.border-hover-navy:hover{border-color: #1f2d3b;}
.border-navy-light{border-color: #33495f;}
.border-hover-navy-light:hover{border-color: #2c3e51;}
.border-purple{border-color: #8f3faf}
.border-hover-purple:hover{border-color: #7b3697;}
.border-purple-light{border-color: #b078c6;}
.border-hover-purple-light:hover{border-color: #8f3faf;}
.border-yellow{border-color: #f3b100;}
.border-hover-yellow:hover{border-color: #e2a50b;;}
.border-yellow-light{border-color: #ffbe12;}
.border-hover-yellow-light:hover{border-color: #f3b100;}
.border-orange{border-color: #e87f04;}
.border-hover-orange:hover{border-color: #df6f0b;;}
.border-orange-light{border-color: #f59d00;}
.border-hover-orange-light:hover{border-color: #e87f04;}
.border-red{border-color: #c23824}
.border-hover-red:hover{border-color: #a9311f;}
.border-red-light{border-color: #ea4b35;}
.border-hover-red-light:hover{border-color: #c23824;}
.border-black{border-color: #000000;}
.border-hover-black:hover{border-color: #000000;}
.border-white{border-color: #ffffff;}
.border-hover-white:hover{border-color: #ffffff;}
.border-grey{border-color: #666666;}
.border-hover-grey:hover{border-color: #555555;}
.border-grey-light{border-color: #999999;}
.border-hover-grey-light:hover{border-color: #888888;}
.border-grey-ultralight{border-color: #f4f4f4;}
.border-hover-grey-ultralight:hover{border-color: #f1f1f1;}
.border-charcoal{border-color: #333333; }
.border-hover-charcoal:hover{border-color: #222222; }
.border-charcoal-light{border-color: #555555;}
.border-hover-charcoal-light:hover{border-color: #444444;}
.border-black{border-color: #000000;}
.border-hover-black:hover{border-color: #000000;}
.border-base{border-color: #eeeeee; }
.border-hover-base:hover{border-color: #d0d0d0;}

/* Social Networks Background Color Classes */
.border-behance,
.border-hover-behance:hover{border-color: #1769ff; color: #1769ff;}
.border-dribbble,
.border-hover-dribbble:hover{border-color: #ea4c89; color: #ea4c89;}
.border-facebook,
.border-hover-facebook:hover{border-color: #3b5998; color: #3b5998;}
.border-flickr,
.border-hover-flickr:hover{border-color: #0063DC; color: #0063DC;}
.border-github,
.border-hover-github:hover{border-color: #333; color: #333;}
.border-google,
.border-hover-google:hover{border-color: #d34836; color: #d34836;}
.border-instagram,
.border-hover-instagram:hover{border-color: #517fa4; color: #517fa4;}
.border-linkedin,
.border-hover-linkedin:hover{border-color: #007bb6; color: #007bb6;}
.border-pinterest,
.border-hover-pinterest:hover{border-color: #cb2027; color: #cb2027;}
.border-skype,
.border-hover-skype:hover{border-color: #12A5F4; color: #12A5F4;}
.border-soundcloud,
.border-hover-soundcloud:hover{border-color: #f80; color: #f80;}
.border-spotify,
.border-hover-spotify:hover{border-color: #7ab800; color: #7ab800;}
.border-tumblr,
.border-hover-tumblr:hover{border-color: #35465c; color: #35465c;}
.border-twitter,
.border-hover-twitter:hover{border-color: #00aced; color: #00aced;}
.border-vimeo,
.border-hover-vimeo:hover{border-color: #1ab7ea; color: #1ab7ea;}
.border-youtube,
.border-hover-youtube:hover{border-color: #bb0000; color: #bb0000;}

/* Background Color Classes */
[class*="bkg-hover-"]:hover *,
[class*="bkg-hover-"]:visited *{
	color: inherit;
}
.bkg-turquoise{background: #00a186; border-color: #00a186;}
.bkg-hover-turquoise:hover{background: #008b73; border-color: #008b73;}
.bkg-turquoise-light{background: #00bd9c; border-color: #00bd9c;}
.bkg-hover-turquoise-light:hover{background: #00a186; border-color: #00a186;}
.bkg-green{background: #1bb05d; border-color: #1bb05d;}
.bkg-hover-green:hover{background: #179851; border-color: #179851;}
.bkg-green-light{background: #1ecd6d; border-color: #1ecd6d;}
.bkg-hover-green-light:hover{background: #1bb05d; border-color: #1bb05d;}
.bkg-blue{background: #227fbb; border-color: #227fbb;}
.bkg-hover-blue:hover{background: #1d6ea3; border-color: #1d6ea3;}
.bkg-blue-light{background: #2c96dd; border-color: #2c96dd;}
.bkg-hover-blue-light:hover{background: #227fbb; border-color: #227fbb;}
.bkg-navy{background:#2c3e51; border-color: #2c3e51;}
.bkg-hover-navy:hover{background: #1f2d3b; border-color: #1f2d3b;}
.bkg-navy-light{background: #33495f; border-color: #33495f;}
.bkg-hover-navy-light:hover{background: #2c3e51; border-color: #2c3e51;}
.bkg-purple{background: #8f3faf; border-color: #8f3faf; }
.bkg-hover-purple:hover{background: #7b3697; border-color: #7b3697;}
.bkg-purple-light{background: #b078c6; border-color: #b078c6;}
.bkg-hover-purple-light:hover{background: #8f3faf; border-color: #8f3faf;}
.bkg-yellow{background: #f3b100; border-color: #f3b100;}
.bkg-hover-yellow:hover{background: #e2a50b; border-color: #e2a50b;}
.bkg-yellow-light{background: #ffbe12; border-color: #ffbe12;}
.bkg-hover-yellow-light:hover{background: #f3b100; border-color: #f3b100;}
.bkg-orange{background: #e87f04; border-color: #e87f04;}
.bkg-hover-orange:hover{background: #df6f0b; border-color: #df6f0b;}
.bkg-orange-light{background: #f59d00; border-color: #f59d00;}
.bkg-hover-orange-light:hover{background: #e87f04; border-color: #e87f04;}
.bkg-red{background: #c23824; border-color: #c23824;}
.bkg-hover-red:hover{background: #a9311f; border-color: #a9311f;}
.bkg-red-light{background: #ea4b35; border-color: #ea4b35;}
.bkg-hover-red-light:hover{background: #c23824; border-color: #c23824;}
.bkg-black{background: #000000; border-color: #000000;}
.bkg-hover-black:hover{background: #000000; border-color: #000000;}
.bkg-white{background: #ffffff; border-color: #ffffff;}
.bkg-hover-white:hover{background: #ffffff; border-color: #ffffff;}
.bkg-grey{background-color: #666666; border-color: #666666;}
.bkg-hover-grey:hover{background-color: #555555; border-color: #555555;}
.bkg-grey-light{background-color: #999999; border-color: #999999;}
.bkg-hover-grey-light:hover{background-color: #888888; border-color: #888888;}
.bkg-grey-ultralight{background-color: #f4f4f4; border-color: #f4f4f4;}
.bkg-hover-grey-ultralight:hover{background-color: #f1f1f1; border-color: #f1f1f1;}
.bkg-charcoal{background-color: #333333; border-color: #333333;}
.bkg-hover-charcoal:hover{background-color: #222222; border-color: #222222;}
.bkg-charcoal-light{background-color: #555555; border-color: #555555;}
.bkg-hover-charcoal-light:hover{background-color: #444444; border-color: #444444;}
.bkg-black{background-color: #000000; border-color: #000000;}
.bkg-hover-black:hover{background-color: #000000; border-color: #000000;}
.bkg-base{background: #eeeeee; border-color: #666666; border-color: #eeeeee;}
.bkg-hover-base:hover{background: #d0d0d0; border-color: #666666; border-color: #eeeeee;}

/* Social Networks Background Color Classes */
.bkg-behance,
.bkg-hover-behance:hover{background-color: #1769ff; border-color: #1769ff;}
.bkg-dribbble,
.bkg-hover-dribbble:hover{background-color: #ea4c89; border-color: #ea4c89;}
.bkg-facebook,
.bkg-hover-facebook:hover{background-color: #3b5998; border-color: #3b5998;}
.bkg-flickr,
.bkg-hover-flickr:hover{background-color: #0063DC; border-color: #0063DC;}
.bkg-github,
.bkg-hover-github:hover{background-color: #333; border-color: #333;}
.bkg-google,
.bkg-hover-google:hover{background-color: #d34836; border-color: #d34836;}
.bkg-instagram,
.bkg-hover-instagram:hover{background-color: #517fa4; border-color: #517fa4;}
.bkg-linkedin,
.bkg-hover-linkedin:hover{background-color: #007bb6; border-color: #007bb6;}
.bkg-pinterest,
.bkg-hover-pinterest:hover{background-color: #cb2027; border-color: #cb2027;}
.bkg-skype,
.bkg-hover-skype:hover{background-color: #12A5F4; border-color: #12A5F4;}
.bkg-soundcloud,
.bkg-hover-soundcloud:hover{background-color: #f80; border-color: #f80;}
.bkg-spotify,
.bkg-hover-spotify:hover{background-color: #7ab800; border-color: #7ab800;}
.bkg-tumblr,
.bkg-hover-tumblr:hover{background-color: #35465c; border-color: #35465c;}
.bkg-twitter,
.bkg-hover-twitter:hover{background-color: #00aced; border-color: #00aced;}
.bkg-vimeo,
.bkg-hover-vimeo:hover{background-color: #1ab7ea; border-color: #1ab7ea;}
.bkg-youtube,
.bkg-hover-youtube:hover{background-color: #bb0000; border-color: #bb0000;}

/* Font Color Classes */
[class*="color-"] *{
	color: inherit !important;
}
.color-turquoise{color: #00a186;}
.color-hover-turquoise:hover{color: #008b73;}
.color-turquoise-light{color: #00bd9c;}
.color-hover-turquoise-light:hover{color: #00a186;}
.color-green{color: #1bb05d;}
.color-hover-green:hover{color: #179851;}
.color-green-light{color: #1ecd6d;}
.color-hover-green-light:hover{color: #1bb05d;}
.color-blue{color: #227fbb;}
.color-hover-blue:hover{color: #1d6ea3;}
.color-blue-light{color: #2c96dd;}
.color-hover-blue-light:hover{color: #227fbb;}
.color-navy{color:#2c3e51;}
.color-hover-navy:hover{color: #1f2d3b;}
.color-navy-light{color: #33495f;}
.color-hover-navy-light:hover{color: #2c3e51;}
.color-purple{color: #8f3faf;}
.color-hover-purple:hover{color: #7b3697;}
.color-purple-light{color: #b078c6;}
.color-hover-purple-light:hover{color: #8f3faf;}
.color-yellow{color: #f3b100;}
.color-hover-yellow:hover{ color: #e2a50b;}
.color-yellow-light{color: #ffbe12;}
.color-hover-yellow-light:hover{color: #f3b100;}
.color-orange{color: #e87f04;}
.color-hover-orange:hover{color: #df6f0b;}
.color-orange-light{ color: #f59d00;}
.color-hover-orange-light:hover{color: #e87f04;}
.color-red{color: #c23824;}
.color-hover-red:hover{color: #a9311f;}
.color-red-light{color: #ea4b35;}
.color-hover-red-light:hover{color: #c23824;}
.color-white{color: #ffffff;}
.color-hover-white:hover{color: #ffffff;}
.color-grey{color: #666666;}
.color-hover-grey:hover{color: #555555;}
.color-grey-light{color: #999999;}
.color-hover-grey-light:hover{color: #888888;}
.color-grey-ultralight{color: #f4f4f4;}
.color-hover-grey-ultralight:hover{color: #f1f1f1;}
.color-charcoal{color: #333333;}
.color-hover-charcoal:hover{color: #222222;}
.color-charcoal-light{color: #555555;}
.color-hover-charcoal-light:hover{color: #444444;}
.color-black{color: #000000;}
.color-hover-black:hover{color: #000000;}
.color-base{color: #666666;}
.color-hover-base:hover{color: #666666;}

/* Social Networks Font Color Classes */
.color-behance,
.color-hover-behance:hover{color: #1769ff;}
.color-dribbble,
.color-hover-dribbble:hover{color: #ea4c89;}
.color-facebook,
.color-hover-facebook:hover{color: #3b5998;}
.color-flickr,
.color-hover-flickr:hover{color: #0063DC;}
.color-github,
.color-hover-github:hover{color: #333;}
.color-google,
.color-hover-google:hover{color: #d34836;}
.color-instagram,
.color-hover-instagram:hover{color: #517fa4;}
.color-linkedin,
.color-hover-linkedin:hover{color: #007bb6;}
.color-pinterest,
.color-hover-pinterest:hover{color: #cb2027;}
.color-skype,
.color-hover-skype:hover{color: #12A5F4;}
.color-soundcloud,
.color-hover-soundcloud:hover{color: #f80;}
.color-spotify,
.color-hover-spotify:hover{color: #7ab800;}
.color-tumblr,
.color-hover-tumblr:hover{color: #35465c;}
.color-twitter,
.color-hover-twitter:hover{color: #00aced;}
.color-vimeo,
.color-hover-vimeo:hover{color: #1ab7ea;}
.color-youtube,
.color-hover-youtube:hover{color: #bb0000;}

/* Form element place holder text color*/
[class*="bkg-"]::-webkit-input-placeholder,
[class*="border-"]::-webkit-input-placeholder,
[class*="color-"]::-webkit-input-placeholder{
	color: inherit;
}

[class*="bkg-"]::-moz-placeholder,
[class*="border-"]::-moz-placeholder,
[class*="color-"]::-moz-placeholder{
	color: inherit;
}

[class*="bkg-"]:-ms-input-placeholder,
[class*="border-"]:-ms-input-placeholder,
[class*="color-"]:-ms-input-placeholder{
	color: inherit;
}

/* Special Color Classes */
.highlight{
	background: #eeeeee;
	border-color: #eeeeee;
}
.info{
	background: #d9edf7;
	color: #31708f;
	border-color:#d9edf7;
}
.success{
	background: #dff0d8;
	color: #3c763d;
	border-color: #dff0d8;
}
.alert{
	background: #fcf8e3;
	color: #8a6d3b;
	border-color: #fcf8e3;
}
.warning{
	background: #f2dede;
	color: #a9444a;
	border-color: #f2dede;
}

/* Shadow Classes */
.shadow-hover:hover{
	-webkit-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
	   -moz-box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
		    box-shadow: 0 0.4rem 2.4rem 0 rgba(0,0,0,.15);
}

/*------------------------------------------------------------------
[33. Animation]
*/

a,
input,
textarea,
.form-element,
.form-element + label:before,
button,
.bar,
.box,
.overlay-info,
.thumbnail,
.thumbnail img,
.thumbnail .background-image{
	-webkit-transition-property: background, border-color, color, opacity, box-shadow, -webkit-transform;
			transition-property: background, border-color, color, opacity, box-shadow, transform;
	-webkit-transition-duration: 300ms;
			transition-duration: 300ms;
	-webkit-transition-timing-function: ease-in-out;
			transition-timing-function: ease-in-out;
}
.no-transition,
.no-transition-reset{
	-webkit-transition-duration: 0ms !important;
			transition-duration: 0ms !important;
}

/*------------------------------------------------------------------
[34. Media Queries]
*/

@media only screen and (max-width: 1140px){

	/* Grid */
	body.boxed .wrapper-inner,
	.row{
		max-width: 96rem;
	}

	/* Sub Menu */
	.navigation li:last-child .sub-menu{
		right: 0;
	}
	
	/* Content grid */
	.content-grid-5 .grid-item,	
	.content-grid-6 .grid-item{
		width: 25%;
	}

	/* Feature Column */
	.feature-column-group .width-3{
		width: 50%;
	}
}
@media only screen and (max-width: 1023px){
	.navigation > ul > li:nth-last-child(2) > .sub-menu{
		right: 0;
	}
	.navigation > ul > li:nth-last-child(2) > .sub-menu ul{
		right: 18rem;
		left: auto;
	}

	/* Content grid */
	.content-grid-5 .grid-item,	
	.content-grid-6 .grid-item{
		width: 33.33333%;
	}	
}
@media only screen and (min-width: 960px){
	.pricing-table .pricing-table-column:not(:first-child):not(.callout){
		border-left: none;
	}
	.table > tbody > tr > td:first-child {
		border-left: none;
	}
}
@media only screen and (max-width: 960px) {

	/* Columns */
	.two-columns-on-tablet > .column[class*="width-"]{
		width: 50%;
	}
	.one-column-on-tablet > .column[class*="width-"]{
		width: 100%;
	}
	.two-columns-on-tablet > .column[class*="width-"],
	.one-column-on-tablet > .column[class*="width-"]{
		margin-left: 0;
		position: static;
		left: 0;
		right: 0;
	}
	.row.flex.v-align-middle.one-column-on-tablet{
		display: block;
	}

	/* Grid */
	body.boxed .wrapper-inner,
	.row{
		max-width: 76rem;
	}

	/* Header */
	header .primary-navigation,
	header .primary-nav-column{
		display: none !important;
	}
	header .logo-column,
	header .secondary-nav-column{
		width: auto;
	}
	header .logo-column{
		float: left;
	}
	header .secondary-nav-column{
		float: right;
	}
	.nav-bar .logo,
	.nav-bar .logo.logo-center{
		margin: 0;
		float: left;
	}

	/* Mobile Access */
	.aux-navigation{
		display: table !important;
		visibility: visible !important;
	}
	
	/* Feature Column */
	.feature-column-group .feature-column{
		padding: 4rem;
	}

	/* Pricing Tables */
	.pricing-table.columns-2 .pricing-table-column,
	.pricing-table.columns-3 .pricing-table-column,
	.pricing-table.columns-4 .pricing-table-column,
	.pricing-table.columns-5 .pricing-table-column{
		width: 50%;
	}
	/* Pricing Tables */
	.pricing-table[class*="border-"].columns-3 .pricing-table-column:last-child,
	.pricing-table[class*="border-"].columns-4 .pricing-table-column:nth-last-child(2),
	.pricing-table[class*="border-"].columns-5 .pricing-table-column:last-child,
	.pricing-table[class*="border-"].columns-5 .pricing-table-column:nth-last-child(3){
		border-left-width: 1px;
	}
	.pricing-table.rounded.columns-3 .pricing-table-column:last-child,
	.pricing-table.rounded.columns-5 .pricing-table-column:last-child{
		border-radius: 0.3rem;
	}
	.pricing-table.rounded.columns-4 .pricing-table-column:nth-last-child(2),
	.pricing-table.rounded.columns-5 .pricing-table-column:nth-last-child(3){
		border-radius: 0.3rem 0 0 0.3rem;
	}

	/* Tables */
	.table:not(.non-responsive){
		max-width: 100%;
		display: inline-block;
		position: relative;
	}
	.table tr:last-child {
		width:100%;
	}
	.table:not(.non-responsive) > thead, 
	.table:not(.non-responsive) > thead > tr{
		display: block;
		float: left;
	}
	.table:not(.non-responsive) > thead > tr > th{
		display: block;
	}
	.table:not(.non-responsive) > tbody{
		display: flex;
		position: relative;
		overflow-x: auto;
		white-space: nowrap;
	}
	.table:not(.non-responsive) > tbody > tr{
		display: inline-block;
		vertical-align: top;
		margin-right: -0.4rem;
	}
	.table:not(.non-responsive) > tbody > tr > td{
		display: block;
	}
	.table:not(.non-responsive) th+th{
		border-left: none;
		border-bottom: 1px solid transparent;
	}
}
@media only screen and (max-width: 768px) {

	/* Grid */
	body.boxed .wrapper-inner,
	.row{
		max-width: 60rem;
	}
	.width-1,
	.width-2, 
	.width-3, 
	.width-4, 
	.width-5, 
	.width-6, 
	.width-7, 
	.width-8, 
	.width-9, 
	.width-10, 
	.width-11, 
	.width-12,
	.two-columns-on-tablet > .column[class*="width-"]{
		width: 100%;
	}
	[class*="offset-"]{
		margin-left: 0;
	}
	.column[class*='push-'],
	.column[class*='pull-']{
		position: static;
		left: 0;
		right: 0;
	}
	.row.flex.v-align-middle{
		display: block;
	}

	/* Content grid */
	[class*="content-grid-"] .grid-item{
		width: 100%;
	}

	/* Login button */
	.dropdown .login-form-container .button{
		width: 100%;
		display: block;
		text-align: center;
	}

	/* Dropdown */
	.left-on-mobile .dropdown-list{
		left: 0;
	}

	/* Button icon */
	button.no-label-on-mobile span:not([class*="icon-"]), 
	.button.no-label-on-mobile span:not([class*="icon-"]){
		display: none;
	}
	button.no-label-on-mobile [class*="icon-"], 
	.button.no-label-on-mobile [class*="icon-"]{
		margin: 0;
	}

	/* Blockquote */
	blockquote {
		width: 100% !important;
	}

	/* Lead */
	.lead{
		font-size: 1.8rem;
		line-height: 1.5;
		font-weight: 200;
	}

	/* Feature Column */
	.feature-column-group .width-3{
		width: 100%;
	}

	/* Timeline */
	.timeline.vertical-indication .timeline-title,
	.timeline.vertical-indication .timeline-description{
		padding-left: 7rem;
	}
	.timeline.vertical-indication .timeline-title:before{
		width: 0.1rem;
		content: "";
		background-color: #e7e7e7;
		position: absolute;
		left: 1.5rem;
		top: 0;
		bottom: 0;
	}
	.timeline.vertical-indication.thick .timeline-title:before{
		width: 0.3rem;
	}
	.timeline.vertical-indication .timeline-title:after{
		left: 1.5rem;
	}
	.timeline.vertical-indication .timeline-title:before,
	.timeline.vertical-indication .timeline-description:before{
		left: 2rem;
	}
	.timeline.vertical-indication.thick .timeline-title:before,
	.timeline.vertical-indication.thick .timeline-description:before{
		left: 1.9rem;
	}
	.timeline.vertical-indication li:last-child .timeline-title:before{
		display: none;
	}

	/* Pricing Tables */
	.pricing-table.columns-3 .pricing-table-column.callout{
		top: 0;
	}
	.pricing-table.columns-3 .pricing-table-column{
		margin-bottom: 0.5rem;
	}

	/* Helper Classes for mobile */
	.hide-on-mobile{
		display: none !important;
		visibility: hidden !important;
	}
	.show-on-mobile{
		display: block !important;
		visibility: visible !important;
	}
	.pull-left-on-mobile{
		float: left !important;
	}
	.pull-right-on-mobile{
		float: right !important;
	}
	.clear-float-on-mobile{
		float: none !important;
	}
	.v-align-top,
	.v-align-top-on-mobile{
		padding-top: 2rem !important;
		align-self: flex-start !important;
		vertical-align: top !important;
	}
	.v-align-bottom,
	.v-align-bottom-on-mobile{
		padding-bottom: 2rem !important;
		align-self: flex-end !important;
		vertical-align: bottom !important;
	}
	.no-padding-on-mobile{
		padding: 0 !important
	}
	.no-background-on-mobile{
		background: none !important;
		border: none !important;
	}
	.thumbnail .v-align-top,
	.thumbnail .v-align-top-on-mobile,
	.thumbnail .v-align-bottom,
	.thumbnail .v-align-bottom-on-mobile{
		padding-top: 0 !important;
		padding-bottom: 0 !important;
	}
	.left-on-mobile{
		text-align: left !important;
	}
	.center-on-mobile{
		text-align: center !important;
	}
	.right-on-mobile{
		text-align: right !important;
	}
	.no-transition-on-mobile{
		-webkit-transition-duration: 0ms !important;
				transition-duration: 0ms !important;
	}
}
@media only screen and (max-width: 600px) {
	
	/* Grid */
	body.boxed .wrapper-inner,
	.row{
		max-width: 48rem;
	}

	/* Pricing Tables */
	.pricing-table.columns-2 .pricing-table-column,
	.pricing-table.columns-3 .pricing-table-column,
	.pricing-table.columns-4 .pricing-table-column,
	.pricing-table.columns-5 .pricing-table-column{
		width: 100%;
	}
	.pricing-table.rounded .pricing-table-column{
		border-radius: 0.3rem !important;
	}
	.pricing-table .pricing-table-column.callout{
		top: 0;
	}
	.pricing-table .pricing-table-column{
		margin-bottom: 0.5rem;
	}
	.pricing-table[class*="border-"] .pricing-table-column{
		border-left-width: 1px;
	}
}
@media only screen and (max-width: 480px) {

	/* Grid */
	body.boxed .wrapper-inner,
	.row{
		max-width: 35rem;
	}

	/* Content grid */
	[class*="content-grid-"] .grid-item{
		width: 100%;
	}

	/* Side navigation */
	.side-navigation-wrapper,
	.side-navigation-inner{
		width: 30rem;
	}
	.element-reveal-left{
		-webkit-transform: translate3d(30rem,0,0);
		        transform: translate3d(30rem,0,0);
	}
	.element-reveal-right{
		-webkit-transform: translate3d(-30rem,0,0);
		        transform: translate3d(-30rem,0,0);
	}
	.side-navigation-wrapper.hide{
		right: 30rem;
	}
	.side-navigation-wrapper.enter-right.hide{
		right: -30rem;
	}
	.element-show-left{
		left: 30rem;
	}
	.element-show-right{
		left: -30rem;
	}

	/* Lists */
	.list-group{
		margin-bottom: 0;
	}

	/* Blockquote */
	blockquote {
		width: 100% !important;
	}

	/* Tabs*/
	.tabs.vertical .tab-nav,
	.tabs.vertical.right .tab-nav,
	.tabs.vertical .tab-panes,
	.tabs.vertical.right .tab-panes{
		width: 100%;
		float: none;
	}
	.tab-nav li{
		width: 100%;
		float: none;
	}
	.tab-nav li a{
		width: 100%;
	}
	.tabs.rounded .tab-nav>li:first-child a{
		border-radius: 0.3rem 0.3rem 0 0 !important;
	}
	.tabs.rounded .tab-nav>li:last-child a{
		border-radius: 0 !important;
	}
	.tabs.rounded .tab-panes{
		border-radius: 0 0 0.3rem 0.3rem;
	}
	.tabs li.active:last-child a{
		border-bottom: 1px solid #eeeeee;
	}
	.tabs.vertical li.active a,
	.tabs.vertical.right li.active a{
		border-left: 1px solid #eeeeee;
	}
	.tabs.vertical .tab-panes,
	.tabs.vertical.right .tab-panes{
		border-top: none;
	}
}
@media only screen and (max-width : 350px) {
    
    /* Grid */
	body.boxed .wrapper-inner,
	.row{
		max-width: 100%;
	}  
}