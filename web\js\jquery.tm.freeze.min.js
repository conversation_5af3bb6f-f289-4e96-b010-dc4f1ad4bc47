!function(a,b,c,d){"use strict";var e=function(b,d){var e=a.extend({},a.fn.freeze.tmfOpts,d);b=a(b),e.extraSpaceTop=b.data("extra-space-top")?b.data("extra-space-top"):e.extraSpaceTop,e.extraSpaceBottom=b.data("extra-space-bottom")?b.data("extra-space-bottom"):e.extraSpaceBottom,e.pushSection=b.data("push-section")?b.data("push-section"):e.pushSection,e.threshold=b.data("threshold")?b.data("threshold"):e.threshold;var g=(a(c).scrollTop(),b.offset().top),i=!1;b.data("startup",!0).data("orignDistance",g).css({width:"100%",display:"flex",flexDirection:"column"}),h(b,e),a(c).on("scroll resize",function(){f(b),b.data("startup")||h(b,e),a(c).width()>=e.breakpoint&&i?(i=!1,g=b.offset().top,b.data("orignDistance",g)):a(c).width()<e.breakpoint&&(i=!0)}),b.wrapAll("<div class='freeze-wrapper' />"),f(b)},f=function(a){var b=a.outerHeight(!0);a.parent().css({height:b+"px"})},g=function(b,d){var e=a(c).width();if(e>=960){var f=a(c).scrollTop(),g=b.data("orignDistance"),h=b.outerHeight(!0),i=Math.abs(a(d.pushSection).offset().top-(g+h)+d.extraSpaceTop),j=g-f-d.extraSpaceTop,k=g+(a(d.pushSection).offset().top-(g+h))-f-d.extraSpaceTop;j<=0||j<=0&&b.data("startup")?(b.addClass("sticky").css({maxWidth:a(b).parent().width()+"px",position:"fixed",top:d.extraSpaceTop+"px",zIndex:5}),k<=0+d.extraSpaceBottom&&b.removeClass("sticky").css({top:i-d.extraSpaceTop-d.extraSpaceBottom+"px",position:"relative",zIndex:5})):(f<=b.offset().top-d.extraSpaceTop&&b.addClass("sticky").css({maxWidth:a(b).parent().width()+"px",position:"fixed",top:d.extraSpaceTop+"px",zIndex:5}),b.offset().top<=g&&b.removeClass("sticky").css({maxWidth:"",position:"",top:"",zIndex:""}))}else b.removeClass("sticky").css({maxWidth:"",position:"",top:"",zIndex:""});b.data("startup",!1)},h=function(a,b){a.data("scrolling")||(requestAnimationFrame(function(){i(a,b)}),a.data("scrolling",!0))},i=function(a,b){j(a,b)||a.data("startup")?a.removeClass("out-of-view"):a.addClass("out-of-view"),g(a,b),a.data("scrolling",!1)},j=function(b,d){var e=a(c).scrollTop(),f=e+a(c).height(),g=b.data("threshold")?parseFloat(b.data("threshold")):d.threshold,h=b.data("ty")?parseFloat(b.data("ty")):0,i=(b.offset().top,b.offset().top-h),j=i+b.outerHeight()-b.outerHeight()*g,k=i+b.outerHeight()*g;return f>=k&&e<=j},k=!1;(navigator.userAgent.match(/Android/i)||navigator.userAgent.match(/webOS/i)||navigator.userAgent.match(/iPhone/i)||navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/iPod/i)||navigator.userAgent.match(/BlackBerry/i)||navigator.userAgent.match(/Windows Phone/i))&&(k=!0),function(){for(var a=0,b=["ms","moz","webkit","o"],d=0;d<b.length&&!c.requestAnimationFrame;++d)c.requestAnimationFrame=c[b[d]+"RequestAnimationFrame"],c.cancelAnimationFrame=c[b[d]+"CancelAnimationFrame"]||c[b[d]+"CancelRequestAnimationFrame"];c.requestAnimationFrame||(c.requestAnimationFrame=function(b,d){var e=(new Date).getTime(),f=Math.max(0,16-(e-a)),g=c.setTimeout(function(){b(e+f)},f);return a=e+f,g}),c.cancelAnimationFrame||(c.cancelAnimationFrame=function(a){clearTimeout(a)})}(),a.fn.freeze=function(b){return this.each(function(){if(k)return!1;var c=a(this);if(!c.data("freeze")){var d=new e(this,b);c.data("freeze",d)}})},a.fn.freeze.tmfOpts={extraSpaceTop:0,extraSpaceBottom:0,pushSection:"footer",threshold:1,breakpoint:960}}(jQuery,document,window);