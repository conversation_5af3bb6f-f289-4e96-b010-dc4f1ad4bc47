!function(a){"use strict";var b=function(b,c){function d(a){return a.toString().replace(/(\d)(?=(\d{3})+$)/g,"$1,")}var e,f=a.extend({},a.fn.counter.tmcOpts,c),g=a(b),h=this;h.startCounter=function(){h.clearCounter();var a=g.data("count-from")?parseFloat(g.data("count-from")):f.from,b=g.data("count-to")?parseFloat(g.data("count-to")):f.to,c=g.data("count-interval")?parseFloat(g.data("count-interval")):f.interval,i=a<=b?"up":"down";e=setInterval(function(){(a===b||isNaN(a)||isNaN(b))&&(clearInterval(e),f.onComplete&&f.onComplete()),g.text(d(a)),"up"===i?a++:a--},c)},h.clearCounter=function(){clearInterval(e);var a=g.data("count-from")?parseFloat(g.data("count-from")):f.from;g.html(a)},f.autoStart&&h.startCounter()};a.fn.counter=function(c){return this.each(function(){var d=a(this);if(!d.data("counter")){var e=new b(this,c);d.data("counter",e)}})},a.fn.counter.tmcOpts={autoStart:!0,from:0,to:100,interval:20,onComplete:null}}(jQuery);