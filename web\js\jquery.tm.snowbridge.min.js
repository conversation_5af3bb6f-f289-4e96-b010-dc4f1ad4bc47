!function(a,b,c,d){"use strict";function e(b,d){var e=a('<div class="tm-parallax" />').prependTo(a(b));r&&e.css({height:""}).addClass("tmp-mobile");var g=a('<div class="tm-loader"><svg id="circle" viewBox="25 25 50 50"><circle class="stroke" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10"/></svg></div>');g.appendTo(e);var h=b.data("src"),i=h.substr(h.lastIndexOf("."));(c.isRetinaDevice()&&d.retinaSupport||c.isRetinaDevice()&&b.is("[data-retina]"))&&(!r&&!d.retinaSupportMobile||r&&d.retinaSupportMobile)&&(h.match(/\.(svg)/i)||(h=b.data("retina")?b.data("retina"):b.data("src").replace(i,d.retinaSuffix+i))),a('<img class="tmp-media"/>').attr("src",h).one("load",function(){a(this).attr("src",h).appendTo(e),e.find(".tm-loader").remove(),f(b,d)}).on("error",function(){console.log("Error src:"+h)})}function f(b,d){var e=b.find(".tm-parallax"),f=b.find(".tmp-media"),g=f.attr("src");e.css({"background-image":"url("+g+")"}),f.hide(),e.css({opacity:0}),o&&k(b)?e.css({visibility:"visible",transitionProperty:"opacity",transitionDuration:"1000ms",opacity:1}):e.css({visibility:"visible"}).animate({opacity:1}),a(c).on("resize",function(){i(b,d),h(b,d)}).resize(),a(c).on("scroll",function(){h(b,d)}),d.onLoaded&&d.onLoaded()}var g=function(b,c){var d=a.extend({},a.fn.snowBridge.tmpOpts,c),f=a(b);f.data("animating",!1).data("setup",!1),d.fadeInOut=!f.is('[data-fade-in-out="false"]')&&d.fadeInOut,d.parallaxFactor>1&&(d.parallaxFactor=1),f.data("setup",!0),e(f,d)},h=function(a,b){a.data("animating")||(c.requestAnimationFrame(function(){j(a,b)}),a.data("animating",!0))},i=function(b,d){var e=b.find(".tm-parallax"),f=a(c).height(),g=(b.height(),(b.height()+f)*d.parallaxFactor);return(d.fullscreen||d.scaleContainer)&&e.css({height:g+"px"}),!!b.data("setup")&&void b.data("scrollDistContainer",g)},j=function(b,d){var e=b.find(".tm-parallax");if(k(b)){if(r)return!1;var f=b.height()+b.offset().top-a(c).scrollTop(),g=b.offset().top-a(c).scrollTop(),h=0-e.height()/2,i=h/2*(f/b.data("scrollDistContainer"))*d.parallaxFactor;if(o?e.css({transform:"translate3d( 0px, "+i+"px, 0px)",visibility:"visible"}).removeClass("out-of-view"):e.css({top:i+"px",visibility:"visible"}),d.fadeInOut){var j,l=a(c).height(),m=d.fadeThreshold>1?.5*b.height():b.height()*d.fadeThreshold;g<=m&&(j=Math.abs(f/m)>1?1:Math.abs(f/m)),g<=l&&g>=l-m&&(j=(l-g)/m>1?1:(l-g)/m),e.css({opacity:(Math.ceil(100*j)/100).toFixed(2)})}}else r||e.css({visibility:"hidden"}).addClass("out-of-view");b.data("animating",!1)},k=function(b){var d=a(c).scrollTop(),e=d+a(c).height(),f=b.offset().top,g=f+b.outerHeight();return e>=f&&d<=g};c.isRetinaDevice=function(){var a="(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-resolution: 1.5dppx)";return!!(this.devicePixelRatio>1||this.matchMedia&&this.matchMedia(a).matches)};var l,m=b.body||b.documentElement,n=m.style,o=n.transition!==d||n.WebkitTransition!==d||n.MozTransition!==d||n.MsTransition!==d||n.OTransition!==d,p=["WebkitTransform","MozTransform","OTransform","msTransform"];for(var q in p)n[p[q]]!==d&&(l="-"+p[q].replace("Transform","").toLowerCase());var r=!1;(navigator.userAgent.match(/Android/i)||navigator.userAgent.match(/webOS/i)||navigator.userAgent.match(/iPhone/i)||navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/iPod/i)||navigator.userAgent.match(/BlackBerry/i)||navigator.userAgent.match(/Windows Phone/i))&&(r=!0),function(){for(var a=0,b=["ms","moz","webkit","o"],d=0;d<b.length&&!c.requestAnimationFrame;++d)c.requestAnimationFrame=c[b[d]+"RequestAnimationFrame"],c.cancelAnimationFrame=c[b[d]+"CancelAnimationFrame"]||c[b[d]+"CancelRequestAnimationFrame"];c.requestAnimationFrame||(c.requestAnimationFrame=function(b,d){var e=(new Date).getTime(),f=Math.max(0,16-(e-a)),g=c.setTimeout(function(){b(e+f)},f);return a=e+f,g}),c.cancelAnimationFrame||(c.cancelAnimationFrame=function(a){clearTimeout(a)})}(),a.fn.snowBridge=function(b){return this.each(function(){var c=a(this);if(!c.data("snowBridge")){var d=new g(this,b);c.data("snowBridge",d)}})},a.fn.snowBridge.tmpOpts={fullscreen:!1,parallaxFactor:.6,fadeInOut:!1,fadeThreshold:.5,retinaSupport:!0,retinaSupportMobile:!1,retinaSuffix:"@2x",onLoaded:null}}(jQuery,document,window);