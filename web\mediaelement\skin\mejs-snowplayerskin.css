.mejs-container {
	position: relative;
	font-family: Helvetica, Arial;
	text-align: left;
	vertical-align: top;
	text-indent: 0;
}
.me-plugin {
	position: absolute;
}
.mejs-embed, .mejs-embed body {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
	background: #000;
	overflow: hidden;
}
.mejs-fullscreen {
	overflow: hidden!important;
}
.mejs-container-fullscreen {
	overflow: hidden;
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
}
.mejs-container-fullscreen .mejs-mediaelement, .mejs-container-fullscreen video {
	width: 100%;
	height: 100%;
}
.mejs-clear {
	clear: both;
}
.mejs-background {
	position: absolute;
	top: 0;
	left: 0;
}
.mejs-mediaelement {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}
.mejs-poster {
	background-size: contain;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	position: absolute;
	top: 0;
	left: 0;
}
:root .mejs-poster img {
	display: none;
}
.mejs-poster img {
	padding: 0;
	border: 0;
	border: 0;
}
.mejs-overlay {
	position: absolute;
	top: 0;
	left: 0;
}
.mejs-overlay-play {
	cursor: pointer;
}
.mejs-overlay-button {
	width: 100px;
	height: 100px;
	margin: -50px 0 0 -50px;
	background: url(bigplay.svg) no-repeat;
	position: absolute;
	top: 50%;
	left: 50%;
}
.no-svg .mejs-overlay-button {
	background-image: url(bigplay.png);
}
.mejs-overlay:hover .mejs-overlay-button {
	background-position: 0 -100px;
}
.mejs-overlay-loading {
	width: 80px;
	height: 80px;
	margin: -40px 0 0 -40px;
	position: absolute;
	top: 50%;
	left: 50%;   
}
.mejs-overlay-loading span {
	width: 80px;
	height: 80px;
	display: block;
	border-radius: 50%;
	background: url(loader.svg) no-repeat 0 0;
	-webkit-animation:mejsloader 1s linear infinite;
	   -moz-animation:mejsloader 1s linear infinite;
		 -o-animation:mejsloader 1s linear infinite;
			animation:mejsloader 1s linear infinite;
}
@-webkit-keyframes mejsloader {
	0% {-webkit-transform: rotate(0deg) scale(1);}
	100% {-webkit-transform: rotate(360deg) scale(1);}
}
@keyframes mejsloader {
	0% {transform: rotate(0deg) scale(1);}
	100% {transform:rotate(360deg) scale(1);}
}
.mejs-container .mejs-controls {
	width: auto;
	height: 30px;
	margin: 0;
	padding: 0;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 3px;
	position: absolute;
	list-style-type: none; 
	bottom: 10px;
	left: 10px;
	right: 10px;  
}
.mejs-container.mejs-audio .mejs-controls{
	width: 100%;
	background: #f9f9f9;
	border-radius: 3px;
	right: auto;
	left: auto;
	bottom: auto;
}
.mejs-minimal-player .mejs-container .mejs-controls{
	background: none;
}
.mejs-container .mejs-controls div {
	width: 30px;
	height: 30px;
	margin: 0;
	padding: 0;
	list-style-type: none;
	background-image: none;
	display: block;
	float: left;
	font-size: 11px;
	line-height: 11px;
	font-family: Helvetica, Arial;
	border: 0;
}
.mejs-container .mejs-controls div:first-child{
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
} 
.mejs-container .mejs-controls div:last-child{
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
} 
.mejs-controls .mejs-button button {
	height: 16px;
	width: 16px;
	margin: 7px;
	padding: 0;
	display: block;
	font-size: 0;
	line-height: 0;
	text-decoration: none;
	cursor: pointer;
	border: 0;
	background: transparent url(controls.svg) no-repeat;
	position: absolute;
}
.mejs-minimal-player .mejs-controls .mejs-button button{
	background: transparent url(controls-white.svg) no-repeat;
}
.no-svg .mejs-controls .mejs-button button{
	background-image: url(controls.png);
}
.mejs-minimal-player .no-svg .mejs-controls .mejs-button button{
	background-image: url(controls-white.png);
}
/*
.mejs-controls .mejs-button button:focus {
	outline: dotted 1px #999;
}*/
.mejs-container .mejs-controls .mejs-time {
	width: auto;
	height: 17px;
	padding: 8px 3px 0 3px;
	color: #666;
	display: block;
	overflow: hidden;
	text-align: center;
	-webkit-box-sizing: content-box;
	   -moz-box-sizing: content-box;
	        box-sizing: content-box;
}
.mejs-minimal-player .mejs-container .mejs-controls .mejs-time {
	color: #fff;
}
.mejs-container .mejs-controls .mejs-time.mejs-currenttime-container{
	padding-left: 8px;
}
.mejs-container .mejs-controls .mejs-time span {
	width: auto;
	margin: 1px 2px 0 0;
	color: #666;
	font-size: 12px;
	line-height: 12px;
	display: block;
	float: left;
}
.mejs-controls .mejs-play,
.mejs-controls .mejs-pause{
	background: #fff;
}
.mejs-container.mejs-audio .mejs-controls .mejs-play,
.mejs-container.mejs-audio .mejs-controls .mejs-pause{
	background: #eee;
}
.mejs-minimal-player .mejs-controls .mejs-play button,
.mejs-controls .mejs-play button {
	background-position: 0 0;
}
.mejs-minimal-player .mejs-controls .mejs-pause button,
.mejs-controls .mejs-pause button {
	background-position: 0 -16px;
}
.mejs-minimal-player .mejs-controls .mejs-stop button,
.mejs-controls .mejs-stop button {
	background-position: -112px 0;
}
.mejs-controls div.mejs-time-rail {
	width: 200px;
	padding-top: 0;
	direction: ltr;
}
.mejs-controls .mejs-time-rail span {
	width: 180px;
	height: 7px;
	display: block;
	position: absolute;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	cursor: pointer;
}
.mejs-minimal-player .mejs-controls .mejs-time-rail span{
	height: 5px;
}
.mejs-controls .mejs-time-rail .mejs-time-total {
	margin: 11px 8px 0 10px;
	background: #333;
}
.mejs-minimal-player .mejs-controls .mejs-time-rail .mejs-time-total{
	margin: 13px 8px 0 10px;
}
.mejs-controls .mejs-time-rail .mejs-time-buffering {
	width: 100%;
	background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
	background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	-webkit-background-size: 15px 15px;
	-moz-background-size: 15px 15px;
	-o-background-size: 15px 15px;
	background-size: 15px 15px;
	-webkit-animation: buffering-stripes 2s linear infinite;
	-moz-animation: buffering-stripes 2s linear infinite;
	-ms-animation: buffering-stripes 2s linear infinite;
	-o-animation: buffering-stripes 2s linear infinite;
	animation: buffering-stripes 2s linear infinite;
}
@-webkit-keyframes buffering-stripes {
	from {
	background-position: 0 0;
}
to {
	background-position: 30px 0;
}
}
@-moz-keyframes buffering-stripes {
	from {
	background-position: 0 0;
}
to {
	background-position: 30px 0;
}
}
@-ms-keyframes buffering-stripes {
	from {
	background-position: 0 0;
}
to {
	background-position: 30px 0;
}
}
@-o-keyframes buffering-stripes {
	from {
	background-position: 0 0;
}
to {
	background-position: 30px 0;
}
}
@keyframes buffering-stripes {
	from {
	background-position: 0 0;
}
to {
	background-position: 30px 0;
}
}
.mejs-controls .mejs-time-rail .mejs-time-loaded {
	width: 0;
	background: #999;
}
.mejs-controls .mejs-time-rail .mejs-time-current {
	width: 0;
	background: #666;
}
.mejs-controls .mejs-time-rail .mejs-time-handle {
	width: 10px;
	margin: 0;
	display: none;
	background: #fff;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	cursor: pointer;
	border: solid 2px #333;
	text-align: center;
	position: absolute;
	top: -2px;
}
.mejs-controls .mejs-time-rail .mejs-time-float {
	width: 36px;
	height: 10px;
	margin-left: -18px;
	padding: 12px;
	background: #000;
	text-align: center;
	color: #fff;
	border: none;
	position: absolute;
	display: none;
	top: -37px;
	-webkit-box-shadow: rgba(0,0,0,0.1) 0 0 5px 5px;
	-moz-box-shadow: rgba(0,0,0,0.1) 0 0 5px 5px;
	box-shadow: rgba(0,0,0,0.1) 0 0 5px 5px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
}
.mejs-controls .mejs-time-rail .mejs-time-float-current {
	width: 100%;
	margin: -4px 0 0 0;
	display: block;
	text-align: center;
	left: 0;
}
.mejs-controls .mejs-time-rail .mejs-time-float-corner {
	width: 16px;
	height: 8px !important;
	margin-left: -8px;
	display: block;
	background: url(controls.svg) no-repeat -80px -5px;
	border: none;
	position: absolute;
	top: auto;
	bottom: -5px;
	left: 50%;
}
.mejs-long-video .mejs-controls .mejs-time-rail .mejs-time-float {
	width: 48px;
}
.mejs-long-video .mejs-controls .mejs-time-rail .mejs-time-float-current {
	width: 44px;
}
.mejs-long-video .mejs-controls .mejs-time-rail .mejs-time-float-corner {
	left: 18px;
}
.mejs-minimal-player .mejs-controls .mejs-fullscreen-button button,
.mejs-controls .mejs-fullscreen-button button {
	background-position: -32px 0;
}
.mejs-minimal-player .mejs-controls .mejs-unfullscreen button,
.mejs-controls .mejs-unfullscreen button {
	background-position: -32px -16px;
}
.mejs-minimal-player .mejs-controls .mejs-mute button,
.mejs-controls .mejs-mute button {
	background-position: -16px -16px;
}
.mejs-minimal-player .mejs-controls .mejs-unmute button,
.mejs-controls .mejs-unmute button {
	background-position: -16px 0;
}
.mejs-controls .mejs-volume-button {
	position: relative;
}
.mejs-controls .mejs-volume-button .mejs-volume-slider {
	width: 25px;
	height: 115px;
	margin: 0;
	display: none;
	background: rgba(255, 255, 255, 0.90);
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0 !important;
	overflow: visible;
	position: absolute;
	top: -115px;
	left: 0;
	z-index: 1;
}
.mejs-controls .mejs-volume-button:hover {
	-webkit-border-radius: 0 0 4px 4px;
	-moz-border-radius: 0 0 4px 4px;
	border-radius: 0 0 4px 4px;
}
.mejs-controls .mejs-volume-button .mejs-volume-slider .mejs-volume-total {
	width: 2px;
	height: 100px;
	margin: 0;
	background: #999;
	position: absolute;
	left: 11px;
	top: 8px;
}
.mejs-controls .mejs-volume-button .mejs-volume-slider .mejs-volume-current {
	width: 2px;
	height: 100px;
	margin: 0;
	background: #666;
	position: absolute;
	left: 11px;
	top: 8px;
}
.mejs-controls .mejs-volume-button .mejs-volume-slider .mejs-volume-handle {
	width: 14px;
	height: 14px;
	margin: 0;
	background: #ffffff;
	border-radius: 50%;
	position: absolute;
	left: 5px;
	cursor: pointer;
}
.mejs-controls div.mejs-horizontal-volume-slider {
	height: 30px;
	width: 60px;
	position: relative;
}
.mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-total {
	width: 50px;
	height: 7px;
	margin: 0;
	padding: 0;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	background: #999;
	font-size: 1px;
	position: absolute;
	left: 0;
	top: 11px;
}
.mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-current {
	width: 50px;
	height: 7px;
	margin: 0;
	padding: 0;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	font-size: 1px;
	border-radius: 2px;
	background: #666;
	position: absolute;
	left: 0;
	top: 11px;
}
.mejs-minimal-player .mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-total,
.mejs-minimal-player .mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-current{
	height: 5px;
	top: 13px;
}
.mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-handle {
	display: none;
}
.mejs-controls .mejs-captions-button {
	position: relative;
}
.mejs-controls .mejs-captions-button button {
	background-position: -48px 0;
}
.mejs-controls .mejs-captions-button .mejs-captions-selector {
	visibility: hidden;
	background: #333;
	border: solid 1px transparent;
	padding: 10px;
	overflow: hidden;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	position: absolute;
	bottom: 26px;
	right: -10px;
	width: 130px;
	height: 100px;
}
.mejs-controls .mejs-captions-button .mejs-captions-selector ul {
	margin: 0;
	padding: 0;
	display: block;
	list-style-type: none!important;
	overflow: hidden;
}
.mejs-controls .mejs-captions-button .mejs-captions-selector ul li {
	margin: 0 0 6px 0;
	padding: 0;
	list-style-type: none!important;
	display: block;
	color: #fff;
	overflow: hidden;
}
.mejs-controls .mejs-captions-button .mejs-captions-selector ul li input {
	margin: 3px 3px 0 5px;
	clear: both;
	float: left;
}
.mejs-controls .mejs-captions-button .mejs-captions-selector ul li label {
	width: 100px;
	padding: 4px 0 0 0;
	float: left;
	line-height: 15px;
	font-family: helvetica, arial;
	font-size: 10px;
}
.mejs-controls .mejs-captions-button .mejs-captions-translations {
	margin: 0 0 5px 0;
	font-size: 10px;
}
.mejs-chapters {
	width: 10000px;
	position: absolute;
	top: 0;
	left: 0;
	-xborder-right: solid 1px #fff;
	z-index: 1;
}
.mejs-chapters .mejs-chapter {
	background: #222;
	background: rgba(0, 0, 0, 0.7);
	background: -webkit-gradient(linear, 0% 0, 0% 100%, from(rgba(50, 50, 50, 0.7)), to(rgba(0, 0, 0, 0.7)));
	background: -webkit-linear-gradient(top, rgba(50, 50, 50, 0.7), rgba(0, 0, 0, 0.7));
	background: -moz-linear-gradient(top, rgba(50, 50, 50, 0.7), rgba(0, 0, 0, 0.7));
	background: -o-linear-gradient(top, rgba(50, 50, 50, 0.7), rgba(0, 0, 0, 0.7));
	background: -ms-linear-gradient(top, rgba(50, 50, 50, 0.7), rgba(0, 0, 0, 0.7));
	background: linear-gradient(rgba(50, 50, 50, 0.7), rgba(0, 0, 0, 0.7));
	filter: progid:DXImageTransform.Microsoft.Gradient(GradientType=0, startColorstr=#323232, endColorstr=#000000);
	overflow: hidden;
	border: 0;
	position: absolute;
	float: left;
}
.mejs-chapters .mejs-chapter .mejs-chapter-block {
	padding: 5px;
	font-size: 11px;
	color: #fff;
	display: block;
	border-right: solid 1px #333;
	border-bottom: solid 1px #333;
	cursor: pointer;
}
.mejs-chapters .mejs-chapter .mejs-chapter-block-last {
	border-right: none;
}
.mejs-chapters .mejs-chapter .mejs-chapter-block:hover {
	background: #666;
	background: rgba(102, 102, 102, 0.7);
	background: -webkit-gradient(linear, 0% 0, 0% 100%, from(rgba(102, 102, 102, 0.7)), to(rgba(50, 50, 50, 0.6)));
	background: -webkit-linear-gradient(top, rgba(102, 102, 102, 0.7), rgba(50, 50, 50, 0.6));
	background: -moz-linear-gradient(top, rgba(102, 102, 102, 0.7), rgba(50, 50, 50, 0.6));
	background: -o-linear-gradient(top, rgba(102, 102, 102, 0.7), rgba(50, 50, 50, 0.6));
	background: -ms-linear-gradient(top, rgba(102, 102, 102, 0.7), rgba(50, 50, 50, 0.6));
	background: linear-gradient(rgba(102, 102, 102, 0.7), rgba(50, 50, 50, 0.6));
	filter: progid:DXImageTransform.Microsoft.Gradient(GradientType=0, startColorstr=#666666, endColorstr=#323232);
}
.mejs-chapters .mejs-chapter .mejs-chapter-block .ch-title {
	margin: 0 0 3px 0;
	font-size: 12px;
	font-weight: bold;
	display: block;
	white-space: nowrap;
	text-overflow: ellipsis;
	line-height: 12px;
}
.mejs-chapters .mejs-chapter .mejs-chapter-block .ch-timespan {
	margin: 3px 0 4px 0;
	font-size: 12px;
	line-height: 12px;
	display: block;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.mejs-captions-layer {
	text-align: center;
	line-height: 22px;
	font-size: 12px;
	color: #fff;
	position: absolute;
	bottom: 0;
	left: 0;
}
.mejs-captions-layer a {
	color: #fff;
	text-decoration: underline;
}
.mejs-captions-layer[lang=ar] {
	font-size: 20px;
	font-weight: normal;
}
.mejs-captions-position {
	width: 100%;
	bottom: 15px;
	position: absolute;
	left: 0;
}
.mejs-captions-position-hover {
	bottom: 45px;
}
.mejs-captions-text {
	padding: 3px 5px;
	background: #333;
	background: rgba(20, 20, 20, 0.8);
}
.me-cannotplay a {
	color: #fff;
	font-weight: bold;
}
.me-cannotplay span {
	padding: 15px;
	display: block;
}
.mejs-controls .mejs-loop-off button {
	background-position: -64px -16px;
}
.mejs-controls .mejs-loop-on button {
	background-position: -64px 0;
}
.mejs-controls .mejs-backlight-off button {
	background-position: -80px -16px;
}
.mejs-controls .mejs-backlight-on button {
	background-position: -80px 0;
}
.mejs-controls .mejs-picturecontrols-button {
	background-position: -96px 0;
}
.mejs-contextmenu {
	width: 150px;
	padding: 10px;
	background: #fff;
	border: solid 1px #999;
	border-radius: 4px;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1001;
}
.mejs-contextmenu .mejs-contextmenu-separator {
	height: 1px;
	margin: 5px 6px;
	font-size: 0;
	background: #333;
}
.mejs-contextmenu .mejs-contextmenu-item {
	padding: 4px 6px;
	font-family: Helvetica, Arial;
	font-size: 12px;
	cursor: pointer;
	color: #333;
}
.mejs-contextmenu .mejs-contextmenu-item:hover {
	background: #2C7C91;
	color: #fff;
}
.mejs-controls .mejs-sourcechooser-button {
	position: relative;
}
.mejs-controls .mejs-sourcechooser-button button {
	background-position: -128px 0;
}
.mejs-controls .mejs-sourcechooser-button .mejs-sourcechooser-selector {
	width: 130px;
	height: 100px;
	visibility: hidden;
	background: #333;
	border: solid 1px transparent;
	padding: 10px;
	overflow: hidden;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	position: absolute;
	bottom: 26px;
	right: -10px;
}
.mejs-controls .mejs-sourcechooser-button .mejs-sourcechooser-selector ul {
	margin: 0;
	padding: 0;
	display: block;
	list-style-type: none!important;
	overflow: hidden;
}
.mejs-controls .mejs-sourcechooser-button .mejs-sourcechooser-selector ul li {
	margin: 0 0 6px 0;
	padding: 0;
	list-style-type: none!important;
	display: block;
	color: #fff;
	overflow: hidden;
}
.mejs-controls .mejs-sourcechooser-button .mejs-sourcechooser-selector ul li input {
	margin: 3px 3px 0 5px;
	clear: both;
	float: left;
}
.mejs-controls .mejs-sourcechooser-button .mejs-sourcechooser-selector ul li label {
	width: 100px;
	padding: 4px 0 0 0;
	line-height: 15px;
	font-family: helvetica, arial;
	font-size: 10px;
	float: left;
}
.mejs-postroll-layer {
	width: 100%;
	height: 100%;
	background: #333;
	overflow: hidden;
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 1000;
}
.mejs-postroll-layer-content {
	width: 100%;
	height: 100%;
}
.mejs-postroll-close {
	padding: 4px;
	background: #333;
	color: #fff;
	cursor: pointer;
	position: absolute;
	right: 0;
	top: 0;
	z-index: 100;
}
