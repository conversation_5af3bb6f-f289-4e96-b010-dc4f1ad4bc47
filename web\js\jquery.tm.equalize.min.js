!function(a){"use strict";var b=function(b,c){var d=a.extend({},a.fn.equalizeHeights.tmeOpts,c),e=a(b),f=this,g=0,h=0,i=0,j=[],k=0,l=e.children(),m=e.data("leader"),n=a('[data-follow="'+m+'"]');f.updateHeights=function(){i=0,d.equalizeByGroup?l.each(function(){h=a(this).position().top,a(this).attr("class",function(a,b){return b.replace(/row-\d+/,"")}).css({height:"auto"}),h!==g&&(i++,k=0,j.length=0),0===i&&(i=1,k=0),j.push(a(this)),k=k>a(this).outerHeight()?k:a(this).outerHeight(),a.each(j,function(a){j[a].addClass("row-"+i).css({height:k+"px"})}),g=h}):(a(this).css({height:"auto"}),k=0,k=e.outerHeight(),n.css({height:k+"px"}))},f.clearHeights=function(){d.equalizeByGroup?l.css({height:"auto"}):n.css({height:"auto"})},a(window).on("resize",function(){return!!d.updateOnResize&&void(a(window).width()>d.clearUnder?f.updateHeights():f.clearHeights())}),f.updateHeights()};a.fn.equalizeHeights=function(c){return this.each(function(){var d=a(this);if(!d.data("equalizeHeights")){var e=new b(this,c);d.data("equalizeHeights",e)}})},a.fn.equalizeHeights.tmeOpts={equalizeByGroup:!0,updateOnResize:!0,clearUnder:479}}(jQuery);