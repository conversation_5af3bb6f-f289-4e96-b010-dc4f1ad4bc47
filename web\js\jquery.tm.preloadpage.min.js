!function(a){"use strict";var b=function(b,d,f){d&&a.extend(n,d);var g=a(b).is("[data-loader-type]")?a(b).data("loader-type"):"default";n.showPercentage=!!a(b).is("[data-show-percentage]")||n.showPercentage,a(b).css({visibility:"visible"}),"function"==typeof f&&(onComplete=f),c()?onComplete():e(b,g)},c=function(){if(n.onetimeLoad){for(var a,b=document.cookie.split("; "),c=0;a=b[c]&&b[c].split("=");c++)if("preloadPage"===a.shift())return a.join("=");return!1}return!1},d=function(a){if(n.onetimeLoad){var b=new Date;b.setDate(b.getDate()+a);var c=null===a?"":"expires="+b.toUTCString();document.cookie="preloadPage=loaded; "+c}},e=function(b,c){var d=a("<div />").attr("class","tm-pageloader-wrapper").css({position:"fixed",top:0,left:0,width:"100%",height:"100%",zIndex:9999999}).appendTo(b),e=a("<div />").attr("class","tm-pageloader").addClass(c).appendTo(d);if("none"!=c){a("<div />").attr("id","tm-pl-bar").css({width:"0%",height:"100%"}).appendTo(e)}if(n.showPercentage){a("<div />").attr("id","tm-pl-percentage").appendTo(e)}f(b)},f=function(b){a(b).find("*:not(script)").each(function(){var b="";if(a(this).css("background-image").indexOf("none")==-1&&a(this).css("background-image").indexOf("-gradient")==-1){if(b=a(this).css("background-image"),b.indexOf("url")!=-1){var c=b.match(/url\((.*?)\)/);b=c[1].replace(/\"/g,"")}}else"img"==a(this).get(0).nodeName.toLowerCase()&&"undefined"!=typeof a(this).attr("src")&&(b=a(this).attr("src"));b.length>0&&(k.push(b),g(b))})},g=function(b){var c=new Image;a(c).one("error",function(){l.push(a(this).attr("src")),h()}).one("load",function(){h()}).attr("src",b)},h=function(){m++;var b=".tm-pageloader-wrapper",c="#tm-pl-bar",e="#tm-pl-percentage",f=Math.round(m/k.length*100);a(c).stop().animate({width:f+"%"},500,"linear"),n.showPercentage&&a(e).text(f+"%"),m>=k.length&&(m=k.length,d(),n.showPercentage&&a(e).text("100%"),a(c).stop().animate({width:"100%"},1e3,"linear",function(){i(b)}),j())},i=function(b){a(b).delay(100).fadeOut(1e3,function(){a(b).remove(),a("body").removeClass("preload-page"),n.onComplete()})},j=function(){if(l.length>0){var a;a+=l.length+" image file/s can not be found. \n\r",a+="Please check:\n\r";for(var b=0;b<l.length;b++)a+="- "+l[b]+"\n\r";return console.log(a),!0}return!1},k=[],l=[],m=0,n={showPercentage:!1,onetimeLoad:!1,onComplete:null};a.fn.preloadPage=function(a,c){return this.each(function(){new b(this,a,c)})}}(jQuery);