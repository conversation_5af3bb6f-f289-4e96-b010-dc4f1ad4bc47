!function(a){"use strict";a.fn.swipeIt=function(b){var c=a.extend({swipeThreshold:40,scrollThreshold:10,draggable:!1,preventTouchOn:"",onSwipeMove:null,onSwipeEnd:null},b),d=this,e=0,f=0,g=c.swipeThreshold,h="ontouchend"in document,i=h?"touchstart":"pointerdown",j=h?"touchmove":"pointermove",k=h?"touchend":"pointermove",l=function(a){a.stopPropagation(),e=a.originalEvent.touches?a.originalEvent.touches[0].pageX:a,d.on(j,m)},m=function(b){if(!a(b.target).closest(c.preventTouchOn).length||""===c.preventTouchOn){if(f=b.originalEvent.touches?b.originalEvent.touches[0].pageX:b,Math.abs(e-f)>c.scrollThreshold&&b.preventDefault(),c.draggable){var g;g=-(e-f),c.onSwipeMove(g)}if(f===e)return!1;d.on(k,n)}},n=function(){var a;Math.abs(f-e)>g&&(a=f>e?"left":"right",c.onSwipeEnd(a)),d.off(j,m),d.off(k,n)};return d.on(i,l),this}}(jQuery);