$(document).ready(function(){"use strict";var a=".header",b=".header-animated",c=".header-sticky",d=".header-background",e=".header-compact",f=".header-in",g=".header-out",h=".header-positioned",i="#bkg-retina-img, #retina-img, [data-2x]",j=".equalize",k=".fullscreen-sections-wrapper",l=".fullscreen-section",m=".scroll-link",n=1e3,o="easeInOutQuart",p=.6,q=".grid-container",r=".grid-item, .masonry-stamp",s=".grid-filter-menu, .grid-filter-dropdown",t="700ms",u="700ms",v=!1,w=[".video-container iframe",".video-container object"],x=["www.youtube.com","player.vimeo.com","http://www.dailymotion.com"],y=".mejs-container audio, .mejs-container video",z=".carousel-slider",A=".content-slider",B=".tm-slider-container.fullscreen",C=".full-width-slider",D=".logo-slider",E=".hero-slider",F=".testimonial-slider",G=".team-slider",H=".recent-slider",I=".slider-aux-nav",J=".slider-aux-prev",K=".slider-aux-next",L=".slider-aux-bullet",M="#tm-lightbox",N=".lightbox-link",O=".form-element",P=".form-response",Q=".form-honeypot",R=".form-submit",S=".signup-form",T=".required-field",U="Please wait.",V="Sending...",W="You have been added to our list!",X="Oh boy an error occurred, please try again.",Y="Please fill out required fields.",Z="Please enter a valid email address.",_=".contact-form",aa=".required-field",ba="Please wait.",ca="Sending...",da="Thank you, your email has been received!",ea="Oh boy an error occurred, please try again.",fa="Please fill out required fields.",ga="Please enter a valid email address.",ha=".map-container",ia=".map-pan-link-container",ja=".map-pan-link",ka=["images/assets/map-marker.png","images/assets/map-marker-2.png"],la=[[40.723301,-74.002988]],ma=["Downtown New York Office<br>44 St. West 32"],na=45,oa=53,pa=12,qa=!1,ra=!1,sa=!1,ta=!0,ua=!0,va=!1,wa=!0,xa=".content",ya=".footer-fixed",za=".footer",Aa=!0,Ba="body",Ca=".fade-location, .logo a, .footer-logo a, .navigation a, .side-navigation a, .overlay-navigation a, .grid .overlay-link, .post-media .overlay-link, .post-title a, .post-read-more a, .pagination-previous, .pagination-next, .breadcrumb a, a.read-more",Da=".no-page-fade, .no-page-fade a, .mail-link, .lightbox-link, .contains-sub-menu, .blog .pagination-previous, .blog .pagination-next, .disabled, .scroll-link, .navigation-show a, a.navigation-show",Ea="webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",Fa={init:function(){$(Ba).removeClass("no-js"),Fa.siteHeader(),Fa.retinize(),Fa.equalize(768),Fa.parallax(".parallax",!0,!1,!0),Fa.fullScreenSection(),Fa.masonry(),Fa.logoSlider(),Fa.heroSlider(),Fa.testimonialSlider(),Fa.teamSlider(),Fa.recentSlider(),Fa.carouselSlider(),Fa.contentSlider(),Fa.fullScreenSlider(),Fa.fullWidthSlider(),Fa.sliderAuxNav(),Fa.lightbox(),Fa.counter(".stat",".stat-counter",!1),Fa.horizon(".horizon",".parallax .horizon","easeInOutQuint",!1,1),Fa.freeze(".freeze"),Fa.videos(),Fa.mediaElement(),Fa.signupForm(),Fa.contactForm(),Fa.enablePlaceHolder();try{document.getElementsByClassName("map-canvas")&&google.maps.event.addDomListener(window,"load",Fa.googleMap)}catch(a){}Fa.fixedFooter(),Fa.pageFade(),Fa.scrollToSection(),$("body").hasClass("preload-page")&&Fa.preloadPage()},retinize:function(){$(i).retinizeImages()},equalize:function(a){imagesLoaded($(j),function(){$(j).equalizeHeights({clearUnder:a})})},parallax:function(a,b,c,d){$(a).snowBridge({scaleContainer:b,scaleUnder:960,scaleMinHeight:400,fullscreen:c,fadeInOut:d,fadeThreshold:.5,retinaSupport:!1,parallaxFactor:.6,onLoaded:function(){Fa.horizon(".horizon","","easeInOutQuint",!1,1)}})},horizon:function(a,b,c,d,e){return!$("body").hasClass("preload-page")&&void $(a).not(b).horizon({easing:c,recurring:d,threshold:e})},freeze:function(a){$(window).one("load",function(){$(a).freeze({breakpoint:960})})},counter:function(a,b,c){$(b).counter({autoStart:!1}),$(a).each(function(){var a=$(this);a.horizon({recurring:c,inView:function(){return!a.find(b).data("counting")&&void a.find(b).each(function(){a.find(b).data("counting",!0);var c=$(this).data("counter");c.startCounter()})},outOfView:function(){return!(!c||!a.find(b).data("counting"))&&void a.find(b).each(function(){a.find(b).data("counting",!1);var c=$(this).data("counter");c.clearCounter()})}})})},carouselSlider:function(){$(z).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:700,autoAdvance:!1,forceFit:!1,scaleMinHeight:"auto",carousel:!0,carouselVisible:3,lazyLoad:!1,navArrows:!0,navPagination:!0,navShowOnHover:!0,retinaSupport:!1})},contentSlider:function(){$(A).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:700,autoAdvance:!1,forceFit:!1,scaleMinHeight:"auto",lazyLoad:!0,navArrows:!0,navPagination:!0,navShowOnHover:!0,retinaSupport:!1})},fullScreenSlider:function(){$(B).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:700,autoAdvance:!1,fullscreen:!0,captionScaling:!1,lazyLoad:!0,navArrows:!0,navPagination:!0,navShowOnHover:!0,respectRatio:!1,retinaSupport:!1})},fullWidthSlider:function(){$(C).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:700,autoAdvance:!1,forceFit:!1,fullwidth:!0,scaleUnder:960,scaleMinHeight:400,captionScaling:!0,lazyLoad:!0,navArrows:!0,navPagination:!0,navShowOnHover:!0,respectRatio:!1,retinaSupport:!1})},logoSlider:function(){$(D).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:700,autoAdvance:!0,carousel:!0,carouselVisible:5,adaptiveHeight:!0,lazyLoad:!1,navArrows:!0,navPagination:!0,showProgressBar:!1,navShowOnHover:!0,retinaSupport:!1})},heroSlider:function(){$(E).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:900,autoAdvance:!1,scaleMinHeight:"auto",carousel:!1,carouselVisible:1,adaptiveHeight:!0,lazyLoad:!1,navArrows:!0,navPagination:!0,navShowOnHover:!1,retinaSupport:!1})},testimonialSlider:function(){$(F).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:900,autoAdvance:!1,scaleMinHeight:"auto",carousel:!0,carouselVisible:1,adaptiveHeight:!0,lazyLoad:!1,navArrows:!1,navPagination:!0,navShowOnHover:!1,retinaSupport:!1})},teamSlider:function(){$(G).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:900,autoAdvance:!1,carousel:!0,carouselVisible:3,adaptiveHeight:!0,lazyLoad:!1,navArrows:!1,navPagination:!0,navShowOnHover:!1,retinaSupport:!1})},recentSlider:function(){$(H).avalancheSlider({animation:"slide",easing:"easeInOutQuart",speed:900,autoAdvance:!1,carousel:!0,carouselVisible:3,adaptiveHeight:!0,lazyLoad:!0,navArrows:!0,navPagination:!0,navShowOnHover:!0,retinaSupport:!1})},sliderAuxNav:function(){$(I).each(function(){var a="#"+$(this).data("target-slider"),b=$(a).data("avalancheSlider");$(this).is(J)?$(this).on("click",function(a){a.preventDefault(),b.prevSlide()}):$(this).is(K)&&$(this).on("click",function(a){a.preventDefault(),b.nextSlide()})}),$(L).each(function(){var a="#"+$(this).data("target-slider"),b=$(a).data("avalancheSlider");$(this).on("click",function(a){a.preventDefault();var c=$(this).data("slide");b.slideTo(c),$(this).siblings().removeClass("active"),$(this).addClass("active")})})},lightbox:function(){$(N).summitLightbox({lightboxAnimation:"slideInTop",contentAnimation:"slide",slideAmount:100,easing:"swing",speed:350,onLoaded:function(){var a=$(N).data("summitLightbox"),b=$(M).find(".tml-aux-exit");if(0!==b.length&&b.on("click",function(b){b.preventDefault(),$(this).css({transition:"none"}),a.destroyLightbox()}),$(".tm-lightbox").find(".wpcf7").length){var c=null;$(".wpcf7 > form").each(function(){var a=$(this);wpcf7.initForm(a),wpcf7.cached&&wpcf7.refill(a)}),$(".tm-lightbox").hasClass("destroy-on-success")&&document.addEventListener("wpcf7mailsent",function(b){clearTimeout(c),c=setTimeout(function(){a.destroyLightbox()},1e3)},!1)}}})},videos:function(){fluidvids.init({selector:w,players:x})},mediaElement:function(){$(y).each(function(){var a=!1;$(this).is("audio")&&(a=!0),$(this).mediaelementplayer({features:a?["playpause","progress","volume","fullscreen"]:["playpause","progress","current","duration","tracks","volume","fullscreen"],videoWidth:"100%",videoHeight:"100%",audioWidth:"100%",videoVolume:"vertical",audioVolume:"horizontal"})})},siteHeader:function(){function i(){j=$(window).width(),k=$(window).height(),l=Math.ceil(u.outerHeight()),m="window-height"===u.data("bkg-threshold")?k-l:u.data("bkg-threshold"),n="window-height"===u.data("compact-threshold")?k-l:u.data("compact-threshold"),o="window-height"===u.data("sticky-threshold")?k-l:u.data("sticky-threshold"),p=u.data("helper-in-threshold"),q=u.data("helper-out-threshold")}var j,k,l,m,n,o,p,q,r,s=0,t=960,u=$(a),v=!!u.is("[data-sticky-threshold]");b=b.split(".").join(""),c=c.split(".").join(""),d=d.split(".").join(""),e=e.split(".").join(""),f=f.split(".").join(""),g=g.split(".").join(""),h=h.split(".").join("");var w=function(b){return!($("body").hasClass("mobile")||j<t)&&(o&&$(b).scrollTop()>=o||0===o?($(a).addClass(c),p&&q&&$(a).addClass(h)):($(a).removeClass(c),p&&q&&$(a).removeClass(h)),m&&$(b).scrollTop()>=m?$(a).addClass(d):$(a).removeClass(d),n&&$(b).scrollTop()>=n?$(a).addClass(e):$(a).removeClass(e),r=$(b).scrollTop(),r>s?(p&&$(b).scrollTop()>=p&&$(a).addClass(f),$(a).removeClass(g)):r<s&&(p&&$(b).scrollTop()<=p&&($(a).removeClass(f),$(a).removeClass(g)),p&&$(b).scrollTop()>=p&&$(b).scrollTop()<=q&&($(a).addClass(g),$(g).on(Ea,function(c){return c.stopPropagation(),c.target===$(b)[0]&&void $(a).removeClass(g)}))),void(s=r))},x=function(b){return i(),!$("body").hasClass("mobile")&&(j>t&&v&&u.css({height:u.outerHeight()+"px"}),j>t&&$(window).scrollTop()>=o?($(a).addClass(c),p&&q&&$(a).addClass(h)):($(a).removeClass(c),p&&q&&$(a).removeClass(h)),j>t&&$(window).scrollTop()<m?$(a).removeClass(d):j>t&&$(window).scrollTop()>m&&$(a).addClass(d),j>t&&$(window).scrollTop()<n?$(a).removeClass(e):j>t&&$(window).scrollTop()>n&&$(a).addClass(e),void(j<t&&$(a).removeClass(e)))};$(window).on("scroll",function(){w($(this))}),$(window).on("resize",function(){x()}),$("body").hasClass("mobile")||$(window).one("load",function(){i(),w($(this)),j>t&&v&&u.css({height:l+"px"}),$(a).addClass(b)})},fullScreenSection:function(){var a=function(){var a;if($(k).find(l).length>1){if($("body").hasClass("mobile"))return!1;var b,c=!$(k).hasClass("no-navigation");if(c){b=$("<div>").addClass("fs-pagination").appendTo($(k));for(var d=1;d<$(k).children().length;d++)a=$(k).children().eq(d-1).attr("id"),$(k).find("#"+a).data("index",d),$(k).find(b).append('<a href="#'+a+'" id="fs-pagination-'+d+'" data-index="'+d+'" class="fs-bullet-nav" />');$(window).on("resize",function(){e()});var e=function(){$(b).css({marginTop:-$(b).outerHeight()/2+"px",opacity:1})};e();var f=$(k).find(".fs-bullet-nav");f.each(function(){$(this).on("click",function(b){if(b.preventDefault(),$(this).hasClass("active"))return!1;parseFloat($(this).data("index"));a=$(this).attr("href"),Fa.scrollPage(a,0)})})}$(window).on("scroll",function(){$(k).find(l).each(function(){g($(this))})});var g=function(a){a.data("fs-scrolling")||(window.requestAnimationFrame(function(){h(a)}),a.data("fs-scrolling",!0))},h=function(a){if(Fa.isElementVisible(a,p)&&c){$(b).css({opacity:1});var d=$(k).find(a).data("index");b.find(".active").removeClass("active"),b.find("#fs-pagination-"+d).addClass("active"),a.hasClass("nav-dark")?b.addClass("nav-dark"):b.removeClass("nav-dark")}c&&Fa.isElementVisible($(k),p)?$(b).css({opacity:1,visibility:"visible"}):$(b).css({opacity:0,visibility:"hidden"}),a.data("fs-scrolling",!1)};$(k).find(l).each(function(){g($(this))})}},b=function(){$(l).each(function(){var a=$(this),b=a.find(".background-image, .background-slider-wrapper");if($(this).is("[data-width],[data-height]"))if($("body").hasClass("mobile")||c<=768){a.addClass("fs-image-scale content-below-on-mobile");var e=a.data("width"),f=a.data("height"),g=a.data("min-height"),h=a.width(),i=e>=f?e/f:f/e,j=e>=f?h/i:h*i;j=g&&j<=g?g:j,a.css({height:"auto"}),b.css({height:j+"px",minHeight:j+"px"})}else a.removeClass("fs-image-scale"),a.css({height:""}),b.css({height:"",minHeight:""});else $("body").hasClass("mobile")||c<=768?b.css({height:d+"px"}):(a.removeClass("fs-image-scale"),a.css({height:""}),b.css({height:"",minHeight:""}))})},c=$(window).width(),d=$(window).height();$(window).on("resize",function(){c=$(window).width(),d=$(this).height(),b()}).resize(),a()},scrollToSection:function(){var b,c=[],d=$(a);$(m).each(function(){if(c.push($(this).attr("href")),d.data("compact-threshold")){var a=d.clone().addClass("header-compact").css({display:"none"});a.appendTo("body"),b=$(a).outerHeight(),a.remove()}else b=d.outerHeight();$(this).on("click",function(a){a.preventDefault();var c=$(this).attr("href"),e=$(this).data("offset")?$(this).data("offset"):$(window).width()<960?-d.outerHeight():-b;Fa.scrollPage(c,e)})}),$(window).one("load",function(){$.each(c,function(a,b){e($(b))})}),$(window).on("scroll",function(){$.each(c,function(a,b){e($(b))})});var e=function(a){a.data("section-scrolling")||(window.requestAnimationFrame(function(){f(a)}),a.data("section-scrolling",!0))},f=function(a){var b="header, .header-sub, .side-navigation-wrapper, .overlay-navigation-wrapper";if(Fa.isElementVisible(a,p)){var c=a.attr("id"),d=$('a[href="#'+c+'"]').closest(b);d&&($(b).find(m).parent().removeClass("current"),$(b).find('a[href="#'+c+'"]').parent().addClass("current"))}else 0===$(".in-view").length&&$(b).find(m).parent().removeClass("current");a.data("section-scrolling",!1)}},isElementVisible:function(a,b){if(b=a.is("[data-visible-threshold]")?a.data("visible-threshold"):p,"undefined"==typeof a.offset())return console.log("template-functions.js@isElementVisible: "+a.selector+" cannot be found in your html page."),!1;var c=$(window).height(),d=$(window).scrollTop(),e=d+$(window).height(),f=a.offset().top,g=(a.height(),f+a.outerHeight()-c*b),h=f+c*b;return a.is($(k))||(e>=h&&d<=g?a.addClass("in-view"):a.removeClass("in-view")),e>=h&&d<=g},scrollPage:function(a,b){$("html, body").animate({scrollTop:$(a).offset().top+b},n,o)},masonry:function(){$(q).each(function(){var a,b,c=$(this),d=$(this).find(".grid"),e=$(c).data("default-filter")?$(c).data("default-filter"):"*";if(c.hasClass("fade-in-progressively")&&d.children().addClass("no-transition"),"*"!==e&&($(s).find(".active").removeClass("active"),$(s).find('[data-filter="'+e+'"]').addClass("active")),c.data("layout-mode")){a=c.data("layout-mode")?c.data("layout-mode"):"masonry",c.is("[data-layout-mode]")&&c.addClass("masonry"),c.is("[data-set-dimensions]")&&c.addClass("masonry-set-dimensions"),v=!!c.is("[data-animate-resize]"),u=c.is("[data-animate-resize-duration]")?c.data("animate-resize-duration")+"ms":u,c.is(".masonry-set-dimensions, .masonry-set-dimensions-2")?(c.is(".full-width.no-margins")&&Fa.masonryWrapperWidth(c,d),b=Fa.masonryColWidth(c,d),Fa.masonryThumbSizes(c,d,Fa.masonryColWidth(c,d))):b=".grid-sizer";var f=parseFloat(c.css("padding-top")),g=$('<div class="tm-loader" style="margin-top: 0; top:'+f+'px"><svg id="circle" viewBox="25 25 50 50"><circle class="stroke" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10"/></svg></div>');g.appendTo(c),imagesLoaded(d,function(){d.isotope({filter:e,itemSelector:".grid-item",isResizeBound:!!v,transitionDuration:t,layoutMode:a,stamp:".masonry-stamp",masonry:{columnWidth:b}}),c.find(g).remove(),d.css({visibility:"visible",minHeight:"initial"}),$("body").hasClass("transition-support")&&d.children().removeClass("no-transition");var f=d.children().length-1;d.children().each(function(a){if(c.is("[data-as-bkg-image]")&&c.is("[data-set-dimensions]")&&!$(this).find(".background-image").length){var b=$(this).find("img"),d=$(this).find("img").attr("style"),e=b.attr("srcset")&&!$("body").hasClass("ie-browser")?b.prop("currentSrc"):b.attr("src");$('<span class="background-image background-cover" style="'+d+'" />').css({"background-image":"url("+e+")"}).insertAfter(b),b.remove()}$(this).find("img").length||$(this).find(".background-image").length||$(this).addClass("no-image"),$("body").hasClass("transition-support")?$(this).delay(100*a).queue(function(){$(this).addClass("animate-in"),$(this).dequeue()}):$(this).delay(100*a).animate({opacity:1},500,"easeInOutQuart",function(){a===f&&c.removeClass("fade-in-progressively")})}),$(window).on("resize",function(){c.removeClass("fade-in-progressively"),c.is(".full-width.no-margins")&&Fa.masonryWrapperWidth(c,d),Fa.masonryThumbSizes(c,d,Fa.masonryColWidth(c,d)),v?d.isotope({transitionDuration:u,masonry:{columnWidth:".grid-sizer"!==b?Fa.masonryColWidth(c,d):".grid-sizer"}}):(d.removeClass("filtering"),d.isotope({transitionDuration:0,masonry:{columnWidth:".grid-sizer"!==b?Fa.masonryColWidth(c,d):".grid-sizer"}}).isotope("layout").isotope({transitionDuration:u}))})})}else d.css({visibility:"visible"})}),$(s).each(function(){var a,b;$(this).find("ul a").on("click",function(c){c.preventDefault(),$(this).closest(s).find(".active").removeClass("active"),$(this).addClass("active"),a=$(this).closest(s).data("target-grid")?$(this).closest(s).data("target-grid"):q,$(a).removeClass("fade-in-progressively"),t=$(a).is("[data-animate-filter-duration]")?$(a).data("animate-filter-duration")+"ms":t,a=$(a).find(".grid"),a.isotope({transitionDuration:t}),b=$(this).attr("data-filter"),$(a).addClass("filtering").isotope({filter:b})}),$(this).find("a").each(function(){if(a=$(this).closest(s).data("target-grid")?$(this).closest(s).data("target-grid"):q,b="*"!=$(this).data("filter")?$(this).data("filter"):".grid-item",$(this).find(".project-count").length>0){var c=$(a).find(b).length;$(this).find(".project-count").html(c)}})})},masonryWrapperWidth:function(a,b){var c=Math.ceil(1.001*a.width());b.css({maxWidth:c+"px",width:c+"px"})},masonryColWidth:function(a,b){var c,d=$(window).width(),e=Math.ceil(a.width()),f=a.is(".full-width.no-margins")?Math.ceil(1.001*e):b.width();return a.is(".full-width.small-margins, .full-width.no-margins")?(b.hasClass("content-grid-2")&&(c=d>=480?f/2:f/1),b.hasClass("content-grid-3")&&(c=d>768?f/3:d<=768&&d>480?f/2:f/1),b.hasClass("content-grid-4")&&(c=d>1140?f/4:d<=1140&&d>768?f/3:d<=768&&d>480?f/2:f/1),b.hasClass("content-grid-5")&&(c=d>1300?f/5:d<=1300&&d>1140?f/4:d<=1140&&d>=768?f/3:d<=768&&d>480?f/2:f/1),b.hasClass("content-grid-6")&&(c=d>1300?f/6:d<=1300&&d>1140?f/4:d<=1140&&d>=768?f/3:d<=768&&d>480?f/2:f/1)):(b.hasClass("content-grid-2")&&(c=d>=600?f/2:f/1),b.hasClass("content-grid-3")&&(c=d>960?f/3:d<=960&&d>600?f/2:f/1),b.hasClass("content-grid-4")&&(c=d>1140?f/4:d<=1140&&d>960?f/3:d<=960&&d>600?f/2:f/1),b.hasClass("content-grid-5")&&(c=d>1300?f/5:d<=1300&&d>1140?f/4:d<=1140&&d>960?f/3:d<=960&&d>600?f/2:f/1),b.hasClass("content-grid-6")&&(c=d>1300?f/6:d<=1300&&d>1140?f/4:d<=1140&&d>960?f/3:d<=960&&d>600?f/2:f/1)),c=Math.floor(c)},masonryThumbSizes:function(a,b,c){var d,e=$(window).width(),f=2,g=$(".masonry-set-dimensions").hasClass("no-margins")?0:parseFloat(b.find(".grid-item").css("padding-left")),h=a.is(".masonry-set-dimensions")?a.is("[data-grid-ratio]")?parseFloat(a.data("grid-ratio")):1.5:1;if(d=Math.floor((c-g)/h+g),a.is(".masonry-set-dimensions")){var i=a.is(".full-width.small-margins, .full-width.no-margins")?480:600;b.find(r).each(function(){$(this).is(".large, .masonry-stamp.large")?$(this).is(".portrait, .masonry-stamp.portrait")?$(this).css({width:e>i?Math.floor(c*f)+"px":c+"px",height:e>i?Math.floor(d*(2*f))+"px":2*d+"px"}):$(this).css({width:e>i?Math.floor(c*f)+"px":c+"px",height:e>i?Math.floor(d*f)+"px":d+"px"}):$(this).is(".wide, .masonry-stamp.wide")?$(this).css({width:e>i?Math.floor(c*f)+"px":c+"px",height:e>i?d+"px":d/2+"px"}):$(this).is(".portrait, .masonry-stamp.portrait")?$(this).css({width:c+"px",height:Math.floor(d*f)+"px"}):$(this).css({width:c+"px",height:d+"px"})})}a.is(".masonry-set-dimensions-2")&&(e>600?b.find(r).each(function(){$(this).hasClass("horizontal")?$(this).hasClass("two-third")?$(this).children(".item-description").length?$(this).css({width:Math.floor(c*(f+1))+"px",height:Math.floor(d)+"px"}):$(this).addClass("no-description").css({width:Math.floor(c*f)+"px",height:Math.floor(d)+"px"}):$(this).css({width:Math.floor(c*f)+"px",height:Math.floor(d)+"px"}):$(this).hasClass("vertical")?$(this).hasClass("two-third")&&$(this).children(".item-description").length?$(this).css({width:c+"px",height:Math.floor(c*(f+1))+"px"}):$(this).css({width:c+"px",height:Math.floor(d*f)+"px"}):$(this).css({width:c+"px",height:Math.floor(d)+"px"})}):b.find(r).each(function(){$(this).find("iframe, video, .tm-slider-container").length?$(this).css({width:c+"px",height:d+"px"}):$(this).css({width:"",height:""})}))},signupForm:function(){$(S).submit(function(a){a.preventDefault();var b=$(this),c=b.parent().find(P),d=b.find(O),e=b.find('input[type="email"]'),f=b.find(Q),g=b.find(R),h=b.attr("action"),i=b.attr("method"),j=b.serialize(),k=!1,l=!1,m=!1;if(T=T.split(".").join(""),d.removeClass(T),d.each(function(){$(this).attr("required")&&($(this).val()||(k=!0,$(this).addClass(T),c.hide().text(Y).fadeIn(200)))}),k||(l=!0),e.val()&&!Fa.isValidEmail(e.val())?(c.hide().text(Z).fadeIn(200),e.addClass(T)):m=!0,""!==f.val())return!1;if(k&&e.val()&&!Fa.isValidEmail(e.val())&&c.hide().text(Y+" "+Z).fadeIn(200),l&&m){var n=$(g).val();$(g).css({width:$(g).outerWidth()+"px"}).val(V).attr("disabled",!0),c.hide().text(U).fadeIn(200),$.ajax({url:h,type:i,data:j,dataType:"json"}).done(function(a){try{a.response===!0?(c.text(W),c.delay(1500).fadeOut(200),d.val("")):c.hide().text(a.json.error_message).fadeIn(200)}catch(a){console.log("error in parsing returned ajax data: "+a),c.hide().text("Error occurred. Please see the console for details.").fadeIn(200)}}).fail(function(a,b,d){console.log("Error occured in processing your request:"),console.log(a),console.log("Text status"),console.log(b),console.log("Error thrown"),console.log(d),console.log("Server response"),console.log(a.status),console.log("Response Text may contain error output from PHP"),console.log(a.responseText),c.hide().text(X+b+" ("+d+")").fadeIn(200)}).always(function(){$(g).css({width:""}).val(n).attr("disabled",!1)})}})},contactForm:function(){$(_).submit(function(a){a.preventDefault();var b=$(this),c=b.parent().find(P),d=b.find(O),e=b.find('input[type="email"]'),f=b.find(Q),g=b.find(R),h=b.attr("action"),i=b.attr("method"),j=[],k=encodeURIComponent(b.find("textarea[name=message]").val()+"\n"),l=[];b.find("input:not([type=submit]),select,textarea").each(function(a,b){var c=$(b),d=c.attr("name");if(c.hasClass("form-aux")&&d){var e=c.data("label")||d,f=c.find("option:selected").text(),g=c.val();!g&&c.is("select")?g=e==f?"Not selected":c.find("option:selected").text():"checkbox"!=c.attr("type")||c.prop("checked")||(g="Not checked"),l.push({name:d,label:encodeURIComponent(e),value:encodeURIComponent(g)})}else d&&"message"!=d&&j.push(d+"="+encodeURIComponent(c.val()))});for(var m=0;m<l.length;m++){var n=encodeURIComponent("\n")+l[m].label+"%3A%20"+l[m].value;k+=n}j.push("message="+k),j=j.join("&");var o=!1,p=!1,q=!1;if(aa=aa.split(".").join(""),d.removeClass(aa),d.each(function(){($(this).attr("required")||$(this).children().attr("required"))&&($(this).is(":checkbox")?$(this).is(":checkbox:checked")||(o=!0,$(this).addClass(aa)):$(this).children().is("select")?$(this).children().val()==$(this).children().find("option:selected").text()&&(o=!0,$(this).addClass(aa)):$(this).val()||(o=!0,$(this).addClass(aa)),o&&c.hide().text(fa).fadeIn(200))}),o||(p=!0),e.val()&&!Fa.isValidEmail(e.val())?(c.hide().text(ga).fadeIn(200),e.addClass(aa)):q=!0,""!==f.val())return!1;if(o&&e.val()&&!Fa.isValidEmail(e.val())&&c.hide().text(fa+" "+ga).fadeIn(200),p&&q){var r=$(g).val();$(g).css({width:$(g).outerWidth()+"px"}).val(ca).attr("disabled",!0),c.hide().text(ba).fadeIn(200),$.ajax({url:h,type:i,data:j,dataType:"json"}).done(function(a){try{if(a.response===!0)c.text(da),c.delay(1500).fadeOut(200),d.val("");else{var b="undefined"==typeof a.json.error_message?"There is a possibility that your message was not sent. Please check up the server or script configuration.":a.json.error_message;c.hide().text(ea+" "+b).fadeIn(200)}}catch(b){console.log("error in parsing returned ajax data: "+b),console.log(a),c.hide().text("Error occurred. Please see the console for details.").fadeIn(200)}}).fail(function(a,b,d){console.log("Error occured in processing your request:"),console.log(a),console.log("Text status"),console.log(b),console.log("Error thrown"),console.log(d),console.log("Server response"),console.log(a.status),console.log("Response Text may contain error output from PHP"),console.log(qXHR.responseText),c.hide().text(ea+" (Please see the console for error details.)").fadeIn(200)}).always(function(){$(g).css({width:""}).val(r).attr("disabled",!1)})}})},isValidEmail:function(a){var b=new RegExp(/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,6}$/i);return b.test(a)},enablePlaceHolder:function(){$("input, textarea").placeholder()},googleMap:function(){$(ha).each(function(){var a=$(this).children().attr("id");ka=$(this).data("icon")?JSON.parse("["+$(this).data("icon")+"]"):ka,la=$(this).data("coordinates")?$(this).data("coordinates"):la,ma=$(this).data("info")?JSON.parse("["+$(this).data("info")+"]"):ma,pa=$(this).data("zoom-level")?parseFloat($(this).data("zoom-level")):pa,wa=!(!$(this).data("style")||"greyscale"!==$(this).data("style"));var b=wa?-100:0,c=!$("body").hasClass("mobile"),d={draggable:c,zoom:pa,center:new google.maps.LatLng(la[0][0],la[0][1]),mapTypeControl:ra,mapTypeControlOptions:{style:google.maps.MapTypeControlStyle.DROPDOWN_MENU,position:google.maps.ControlPosition.TOP_RIGHT},panControl:sa,panControlOptions:{position:google.maps.ControlPosition.TOP_LEFT},zoomControl:ta,zoomControlOptions:{style:google.maps.ZoomControlStyle.SMALL,position:google.maps.ControlPosition.LEFT_TOP},scrollwheel:qa,scaleControl:ua,streetViewControl:va,streetViewControlOptions:{position:google.maps.ControlPosition.LEFT_TOP},styles:[{stylers:[{saturation:b}]}]},e=new google.maps.Map(document.getElementById(a),d);$(this).children().data("mapref",e);for(var f,g,h=0,i=0;i<la.length;i++)f=new google.maps.Marker({position:new google.maps.LatLng(la[i][0],la[i][1]),map:e,icon:new google.maps.MarkerImage(ka[h],null,null,null,new google.maps.Size(na,oa))}),g=new google.maps.InfoWindow({content:ma[i]}),google.maps.event.addListener(f,"click",function(a,b){return function(){g.setContent(ma[b]),g.open(e,a)}}(f,i)),h++;google.maps.event.addDomListener(window,"resize",function(){var a=e.getCenter();google.maps.event.trigger(e,"resize"),e.setCenter(a)})}),$(ja).each(function(){$(this).data("marker",!1),$(this).on("click",function(a){a.preventDefault();var b,c,d=$("#"+$(this).data("target-map")).data("mapref"),e=$(this).data("coordinates"),f=new google.maps.LatLng(e[0][0],e[0][1]),g=$(this).data("icon")?JSON.parse("["+$(this).data("icon")+"]"):null,h=$(this).data("icon")?JSON.parse("["+$(this).data("info")+"]"):null;d.panTo(f),$(this).data("marker")||($(this).data("icon")&&(b=new google.maps.Marker({position:new google.maps.LatLng(e[0][0],e[0][1]),map:d,icon:new google.maps.MarkerImage(g[0],null,null,null,new google.maps.Size(na,oa))})),$(this).data("info")&&(c=new google.maps.InfoWindow({content:h[0]}),google.maps.event.addListener(b,"click",function(a,b){return function(){c.setContent(h[0]),c.open(d,a)}}(b,0))),$(this).data("marker",!0)),$(this).closest(ia).hasClass(ia.split(".").join(""))&&($(this).closest(ia).find(".active").removeClass("active"),$(this).addClass("active"))})})},fixedFooter:function(){function a(){$(za).hasClass(d)&&$(window).width()>960&&$(xa).css({marginBottom:c+"px"}),$(za).is("[data-animate-reveal]")&&(b=$(xa).outerHeight()+e-$(window).scrollTop(),b<=$(window).height()&&$(window).width()>960?$(xa).addClass("animate-content"):$(xa).removeClass("animate-content"))}if($("body").hasClass("mobile"))return!1;var b,c=$(ya).outerHeight(),d=ya.split(".").join(""),e=$(xa).offset().top;$(za).hasClass(d)&&$(xa).addClass("reveal-footer"),$(window).on("scroll",function(){a()}),$(window).on("resize",function(){c=$(ya).outerHeight(),a()}),a()},pageFade:function(){return!!Aa&&void $(Ca).each(function(){"_blank"===$(this).attr("target")&&$(this).addClass("no-page-fade"),$(this).not(Da).on("click",function(a){a.preventDefault();var b=this.href;$("body").hasClass("transition-support")?$(Ba).addClass("page-fade-out").on(Ea,function(a){return a.stopPropagation(),a.target===$(this)[0]&&void Fa.goToNewPage(b)}):Fa.goToNewPage(b)})})},goToNewPage:function(a){window.location=a},preloadPage:function(a){$("body").preloadPage({onComplete:function(){Fa.horizon(".horizon",".parallax .horizon","easeInOutQuint",!1,1)}})}};!function(){for(var a=0,b=["ms","moz","webkit","o"],c=0;c<b.length&&!window.requestAnimationFrame;++c)window.requestAnimationFrame=window[b[c]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[b[c]+"CancelAnimationFrame"]||window[b[c]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(b,c){var d=(new Date).getTime(),e=Math.max(0,16-(d-a)),f=window.setTimeout(function(){b(d+e)},e);return a=d+e,f}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(a){clearTimeout(a)})}(),Fa.init()});